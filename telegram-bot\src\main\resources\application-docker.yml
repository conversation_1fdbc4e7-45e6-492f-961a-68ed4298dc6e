spring:
  # Database configuration for Docker
  datasource:
    url: jdbc:mysql://${DB_HOST:mysql}:${DB_PORT:3306}/${DB_NAME:youtube_analytics}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC
    username: ${DB_USER:youtube_user}
    password: ${DB_PASSWORD:youtube_pass}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Kafka configuration for Docker
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka:9092}

# Logging configuration for Docker
logging:
  level:
    com.youtube.telegram: INFO
    org.springframework.kafka: WARN
    org.telegram: WARN
    root: INFO
