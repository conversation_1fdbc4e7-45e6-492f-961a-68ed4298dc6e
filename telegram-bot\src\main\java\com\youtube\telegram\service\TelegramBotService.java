package com.youtube.telegram.service;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.youtube.shared.model.CommentData;

@Service
public class TelegramBotService {

    private static final Logger logger = LoggerFactory.getLogger(TelegramBotService.class);
    private static final String TELEGRAM_API_URL = "https://api.telegram.org/bot";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Value("${telegram.bot.token}")
    private String botToken;
    
    @Value("${telegram.bot.chat-id:@youtube_analytics_test}")
    private String defaultChatId;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final AtomicLong messagesSent = new AtomicLong(0);

    public TelegramBotService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Send formatted comment message to Telegram
     */
    public boolean sendCommentMessage(CommentData commentData) {
        try {
            String message = formatCommentMessage(commentData);
            return sendMessage(message);
        } catch (Exception e) {
            logger.error("Failed to send comment message to Telegram", e);
            return false;
        }
    }

    /**
     * Format comment data into readable Telegram message
     */
    private String formatCommentMessage(CommentData commentData) {
        StringBuilder message = new StringBuilder();
        
        message.append("🎥 *YouTube Video Analysis*\n\n");
        
        // Video information
        message.append("📹 *Video:* ").append(escapeMarkdown(commentData.getVideoTitle() != null ? commentData.getVideoTitle() : "Unknown Video")).append("\n");
        message.append("🔗 *Link:* [Watch Video](").append(commentData.getVideoUrl()).append(")\n\n");
        
        // Channel information (accessed through channelMetadata)
        if (commentData.getChannelMetadata() != null) {
            message.append("📊 *Channel Info:*\n");
            message.append("• *Name:* ").append(escapeMarkdown(commentData.getChannelMetadata().getChannelName())).append("\n");
            message.append("• *Subscribers:* ").append(formatNumber(commentData.getChannelMetadata().getSubscribersCount())).append("\n");
            message.append("• *Total Videos:* ").append(formatNumber(commentData.getChannelMetadata().getTotalVideos())).append("\n");
            message.append("• *Total Comments:* ").append(formatNumber(commentData.getChannelMetadata().getTotalComments())).append("\n");
            message.append("• *Total Likes:* ").append(formatNumber(commentData.getChannelMetadata().getTotalLikes())).append("\n\n");
        }
        
        // Comment information
        message.append("💬 *Comment (").append(commentData.getCommentLength()).append(" chars):*\n");
        message.append("_").append(escapeMarkdown(commentData.getCommentText())).append("_\n\n");
        
        // Comment stats
        message.append("👍 *Likes:* ").append(formatNumber(commentData.getLikesCount() != null ? commentData.getLikesCount() : 0)).append("\n");
        if (commentData.getAuthorName() != null) {
            message.append("👤 *Author:* ").append(escapeMarkdown(commentData.getAuthorName())).append("\n");
        }
        if (commentData.getTimestamp() != null) {
            message.append("⏰ *Processed:* ").append(commentData.getTimestamp().format(DATE_FORMATTER)).append("\n");
        }
        
        message.append("\n---\n");
        message.append("📈 Message #").append(messagesSent.incrementAndGet());
        
        return message.toString();
    }

    /**
     * Send message to Telegram
     */
    private boolean sendMessage(String text) {
        try {
            String url = TELEGRAM_API_URL + botToken + "/sendMessage";
            
            Map<String, Object> body = new HashMap<>();
            body.put("chat_id", defaultChatId);
            body.put("text", text);
            body.put("parse_mode", "Markdown");
            body.put("disable_web_page_preview", true);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.debug("Successfully sent message to Telegram");
                return true;
            } else {
                logger.error("Failed to send message to Telegram. Status: {}, Response: {}", 
                           response.getStatusCode(), response.getBody());
                return false;
            }
            
        } catch (Exception e) {
            logger.error("Error sending message to Telegram", e);
            return false;
        }
    }

    /**
     * Send test message to verify bot connectivity
     */
    public boolean sendTestMessage() {
        String testMessage = "🤖 *YouTube Comment Bot is Online!*\n\n" +
                           "Ready to receive and forward odd-length comments from YouTube videos.\n" +
                           "Messages sent: " + messagesSent.get();
        return sendMessage(testMessage);
    }

    /**
     * Get bot information
     */
    public Map<String, Object> getBotInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("messagesSent", messagesSent.get());
        info.put("botToken", botToken != null && !botToken.isEmpty() ? "Configured" : "Not configured");
        info.put("chatId", defaultChatId);
        return info;
    }

    /**
     * Escape markdown special characters
     */
    private String escapeMarkdown(String text) {
        if (text == null) return "";
        return text.replaceAll("([*_\\[\\]()~`>#+\\-=|{}.!])", "\\\\$1");
    }

    /**
     * Format numbers with commas
     */
    private String formatNumber(long number) {
        return String.format("%,d", number);
    }
} 