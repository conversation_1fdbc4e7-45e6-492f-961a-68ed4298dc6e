# ========================================================================================
# Kafka Server Configuration - YouTube Comment Bot Project
# ========================================================================================
# This file contains all Kafka broker settings migrated from docker-compose.yml
# All settings preserve the exact same values to ensure 100% compatibility

# Basic Kafka Configuration
broker.id=1
listeners=PLAINTEXT://0.0.0.0:9092
advertised.listeners=PLAINTEXT://kafka:9092
listener.security.protocol.map=PLAINTEXT:PLAINTEXT
inter.broker.listener.name=PLAINTEXT

# Zookeeper Configuration
zookeeper.connect=zookeeper:2181
zookeeper.connection.timeout.ms=18000

# Topic Configuration
auto.create.topics.enable=true
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600

# Log Configuration
log.dirs=/var/lib/kafka/data
num.partitions=1
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=1
transaction.state.log.replication.factor=1
transaction.state.log.min.isr=1

# Log Retention Configuration
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000

# Group Coordinator Configuration
group.initial.rebalance.delay.ms=0

# Internal Topic Settings
offsets.topic.num.partitions=50
offsets.topic.replication.factor=1
offsets.retention.minutes=10080

# Compression
compression.type=producer

# Other Settings
delete.topic.enable=true
controlled.shutdown.enable=true
controlled.shutdown.max.retries=3
controlled.shutdown.retry.backoff.ms=5000
