package com.youtube.telegram.controller;

import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youtube.telegram.service.TelegramBotService;

@RestController
public class TelegramController {

    private final TelegramBotService telegramBotService;

    public TelegramController(TelegramBotService telegramBotService) {
        this.telegramBotService = telegramBotService;
    }

    /**
     * Get bot status and information
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        return telegramBotService.getBotInfo();
    }

    /**
     * Send test message to Telegram
     */
    @PostMapping("/test")
    public Map<String, Object> sendTestMessage() {
        boolean success = telegramBotService.sendTestMessage();
        return Map.of(
            "success", success,
            "message", success ? "Test message sent successfully!" : "Failed to send test message"
        );
    }
} 