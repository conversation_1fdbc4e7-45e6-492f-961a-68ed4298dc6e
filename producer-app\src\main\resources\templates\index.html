<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Analytics System - Producer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .btn-custom {
            border-radius: 25px;
            padding: 10px 30px;
            font-weight: 600;
        }
        .stat-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .url-input {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }
        .url-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active { background-color: #28a745; }
        .status-inactive { background-color: #dc3545; }
    </style>
</head>
<body class="bg-light">

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fab fa-youtube text-danger"></i>
                        YouTube Analytics System
                    </h1>
                    <p class="lead mb-4">
                        Submit 5 YouTube video links and get real-time analytics with Kafka streaming to Telegram Bot and Analytics Dashboard.
                    </p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="badge bg-light text-dark p-3 rounded-pill">
                        <i class="fas fa-clock"></i>
                        <span th:text="${schedulerStats?.intervalDescription}">Every 30 seconds</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container my-5">
        
        <!-- Alert Messages -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            <span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div th:if="${errorMessages}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                <li th:each="error : ${errorMessages}" th:text="${error}"></li>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- Simple Status -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-link fa-2x mb-2"></i>
                        <h4 th:text="${stats?.activeLinks ?: 0}">0</h4>
                        <p class="mb-0">Active Video Links</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4>Every 30s</h4>
                        <p class="mb-0">Data Fetch Interval</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Form Section -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-plus-circle"></i>
                            Submit YouTube Video Links
                        </h3>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/submit}" method="post">
                            <div class="mb-3">
                                <p class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Please provide exactly 5 YouTube video URLs. The system will fetch video metadata and analyze the 5 newest comments every 30 seconds.
                                </p>
                            </div>
                            
                            <div th:each="i : ${#numbers.sequence(0, 4)}" class="mb-3">
                                <label th:for="'url' + ${i}" class="form-label fw-semibold">
                                    YouTube URL #<span th:text="${i + 1}">1</span>
                                </label>
                                <input type="url" 
                                       th:id="'url' + ${i}"
                                       th:name="videoUrls"
                                       th:value="${videoUrls != null and videoUrls.size() > i} ? ${videoUrls[i]} : ''"
                                       class="form-control url-input"
                                       placeholder="https://www.youtube.com/watch?v=..."
                                       required>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                                <button type="submit" class="btn btn-primary btn-custom">
                                    <i class="fas fa-upload"></i>
                                    Submit Links
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-custom" onclick="clearForm()">
                                    <i class="fas fa-eraser"></i>
                                    Clear Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Simple Info Panel -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i>
                            How It Works
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6 class="fw-bold text-primary">
                                <i class="fas fa-step-forward"></i> Step 1
                            </h6>
                            <p class="small mb-2">Submit exactly 5 YouTube video links</p>
                        </div>

                        <div class="mb-3">
                            <h6 class="fw-bold text-success">
                                <i class="fas fa-step-forward"></i> Step 2
                            </h6>
                            <p class="small mb-2">System fetches 5 newest comments every 30 seconds</p>
                        </div>

                        <div class="mb-3">
                            <h6 class="fw-bold text-warning">
                                <i class="fas fa-step-forward"></i> Step 3
                            </h6>
                            <p class="small mb-2">Odd-length comments → Telegram Bot</p>
                            <p class="small mb-0">Even-length comments → Analytics Dashboard</p>
                        </div>

                        <hr>

                        <form th:action="@{/clear}" method="post"
                              onsubmit="return confirm('Clear all links and start fresh?')">
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-refresh"></i>
                                Reset & Start Fresh
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">
                <i class="fab fa-youtube text-danger"></i>
                YouTube Analytics System - Real-time data processing with Kafka
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function clearForm() {
            document.querySelectorAll('input[name="videoUrls"]').forEach(input => {
                input.value = '';
            });
        }

        // Auto-refresh status every 30 seconds
        setInterval(function() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    console.log('System status:', data);
                })
                .catch(error => console.error('Error fetching status:', error));
        }, 30000);
    </script>
</body>
</html> 