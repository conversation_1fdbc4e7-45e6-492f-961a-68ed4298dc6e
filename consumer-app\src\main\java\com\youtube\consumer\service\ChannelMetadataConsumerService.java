package com.youtube.consumer.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import com.youtube.shared.model.ChannelMetadata;

@Service
public class ChannelMetadataConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(ChannelMetadataConsumerService.class);

    // In-memory storage for channel metadata (deduplicates by channel ID)
    // Changed to store video-based metadata instead of channel-based
    private final Map<String, ChannelMetadata> videoMetadataMap = new ConcurrentHashMap<>();
    private final Map<String, List<String>> channelVideoMapping = new ConcurrentHashMap<>();
    private volatile boolean newDataReceived = false;

    /**
     * Consume even-length metadata from Kafka and store for analytics
     */
    @KafkaListener(topics = "youtube-even-metadata", groupId = "youtube-analytics-group-v2")
    public void consumeChannelMetadata(ChannelMetadata metadata) {
        logger.info("Received video metadata from Kafka: Channel '{}' for video '{}'",
                   metadata.getChannelName(), metadata.getVideoId());

        try {
            String videoId = metadata.getVideoId();
            String channelId = metadata.getChannelId();

            // Store metadata for each video individually (no deduplication by channel)
            if (videoId != null) {
                videoMetadataMap.put(videoId, metadata);
                logger.info("Stored metadata for video: {} from channel: {}", videoId, metadata.getChannelName());

                // Track which video belongs to this channel for mapping purposes
                List<String> videos = channelVideoMapping.computeIfAbsent(channelId, k -> new ArrayList<>());
                if (!videos.contains(videoId)) {
                    videos.add(videoId);
                    logger.debug("Added video {} to channel {} (total videos from this channel: {})",
                               videoId, metadata.getChannelName(), videos.size());
                }
            } else {
                logger.warn("Received metadata without video ID, skipping");
            }

            // Mark that new data has been received
            newDataReceived = true;

            logger.debug("Successfully processed video metadata. Total videos stored: {}",
                       videoMetadataMap.size());

        } catch (Exception e) {
            logger.error("Error processing video metadata from Kafka", e);
        }
    }

    /**
     * Get all video metadata (one entry per video)
     */
    public List<ChannelMetadata> getAllChannelMetadata() {
        return new ArrayList<>(videoMetadataMap.values());
    }

    /**
     * Get metadata for a specific video
     */
    public ChannelMetadata getVideoMetadata(String videoId) {
        return videoMetadataMap.get(videoId);
    }

    /**
     * Get metadata for a specific channel (returns first video from that channel)
     */
    public ChannelMetadata getChannelMetadata(String channelId) {
        List<String> videos = channelVideoMapping.get(channelId);
        if (videos != null && !videos.isEmpty()) {
            return videoMetadataMap.get(videos.get(0));
        }
        return null;
    }

    /**
     * Get video count for a specific channel
     */
    public int getVideoCountForChannel(String channelId) {
        List<String> videos = channelVideoMapping.get(channelId);
        return videos != null ? videos.size() : 0;
    }

    /**
     * Get all video IDs for a specific channel
     */
    public List<String> getVideosForChannel(String channelId) {
        return channelVideoMapping.getOrDefault(channelId, new ArrayList<>());
    }

    /**
     * Clear all stored data (when new submission arrives)
     */
    public void clearAll() {
        logger.info("=== CLEARING ALL CONSUMER DATA FOR NEW SUBMISSION ===");
        int videoCount = videoMetadataMap.size();
        int totalVideos = channelVideoMapping.values().stream().mapToInt(List::size).sum();

        // Clear all data structures
        videoMetadataMap.clear();
        channelVideoMapping.clear();
        newDataReceived = true; // Set to true to trigger UI refresh

        logger.info("Successfully cleared {} videos and {} video mappings. Ready for new data.",
                   videoCount, totalVideos);
        logger.info("=== CONSUMER RESET COMPLETE ===");
    }

    /**
     * Check if new data has been received since last check
     */
    public boolean hasNewData() {
        return newDataReceived;
    }

    /**
     * Mark data as processed (reset new data flag)
     */
    public void markDataAsProcessed() {
        newDataReceived = false;
    }

    /**
     * Get summary statistics
     */
    public Map<String, Object> getStats() {
        int totalChannels = channelVideoMapping.size(); // Number of unique channels
        int totalVideos = videoMetadataMap.size(); // Total number of videos stored

        return Map.of(
            "totalChannels", totalChannels,
            "totalVideos", totalVideos,
            "newDataReceived", newDataReceived,
            "channels", channelVideoMapping.keySet()
        );
    }
} 