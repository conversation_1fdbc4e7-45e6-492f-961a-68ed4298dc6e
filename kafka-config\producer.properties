# ========================================================================================
# Kafka Producer Configuration - YouTube Comment Bot Project
# ========================================================================================
# This file contains producer settings migrated from application.yml files
# Used by: producer-app (youtube-producer service)

# Bootstrap Configuration
bootstrap.servers=kafka:9092

# Serialization Configuration
# These settings match exactly what's in producer-app/src/main/resources/application.yml
key.serializer=org.apache.kafka.common.serialization.StringSerializer
value.serializer=org.springframework.kafka.support.serializer.JsonSerializer

# JSON Serialization Settings
# Matches: spring.json.add.type.headers: true
spring.json.add.type.headers=true

# Producer Performance Settings
acks=1
retries=3
retry.backoff.ms=100
batch.size=16384
linger.ms=5
buffer.memory=33554432

# Compression
compression.type=snappy

# Idempotence (ensures exactly-once semantics)
enable.idempotence=true
max.in.flight.requests.per.connection=5

# Timeout Settings
request.timeout.ms=30000
delivery.timeout.ms=120000

# Error Handling
max.block.ms=60000

# Metrics
metrics.sample.window.ms=30000
metrics.num.samples=2

# Security (if needed in future)
# security.protocol=PLAINTEXT

# ======================
# TOPIC-SPECIFIC SETTINGS
# ======================
# These can be overridden per topic if needed

# For youtube-odd-comments topic
# Optimized for real-time delivery to Telegram
youtube-odd-comments.acks=1
youtube-odd-comments.compression.type=snappy

# For youtube-even-metadata topic  
# Optimized for reliable delivery to consumer app
youtube-even-metadata.acks=1
youtube-even-metadata.compression.type=snappy

# For youtube-even-comments topic
# Optimized for reliable delivery to consumer app
youtube-even-comments.acks=1
youtube-even-comments.compression.type=snappy

# For youtube-control topic
# Optimized for reliable control message delivery
youtube-control.acks=all
youtube-control.compression.type=snappy

# ======================
# SPRING BOOT INTEGRATION
# ======================
# These properties are used when Spring Boot loads this file
# They correspond to spring.kafka.producer.* properties in application.yml
