[![Review Assignment Due Date](https://classroom.github.com/assets/deadline-readme-button-22041afd0340ce965d47ae6ef1cefeee28c7c493a6346c4f15d667ab976d596c.svg)](https://classroom.github.com/a/gdOVIhcR)
## Requirements for Group Project
[Read the instruction](https://github.com/STIWK3014-A242/class-activity-stiwk3014/blob/main/GroupProject.md)

## Refer to the link below for the `Group Name` and `Group Members`
https://github.com/STIWK3014-A242/class-activity-stiwk3014/blob/main/NewGroupMembers.md

## Group Info:
1. Matric Number & Name & Photo & Phone Number
1. Mention who the leader is.
1. Mention your group name for Assignment-1 and Assignment-2
1. Other related info (if any)

| Group Member    | #1    | #2    | #3    | #4    | #5    | #6    | #7    |
| :---:   | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
| Name    |  (LEADER) Ng <PERSON><PERSON> |  |  |  |  |  |  |
| Matric Number | 293314 |  |  |  |  |  |  |
| Phone Number | 018-3780532 |  |  |  |  |  |  |
| Picture | <img src="https://github.com/user-attachments/assets/a902ef44-f8df-4017-80e1-b6c21f3ab230" width="100"/> |  |  |  |  |  |  |
| Group Name for Ass1 & Ass2 | nezha1, nezha2 |  |  |  |  |  |  |

## Title of your application (a unique title)
## Abstract (in 300 words)
   1. Background
   2. Problem Statement (from article)
   3. Main objective
   4. Methodology
   5. Result
   6. Conclusion

## System Architecture (MUST be included in your presentation)
![image](https://github.com/user-attachments/assets/4de110b6-db0b-494f-b41c-6e2a881a67f0)

## Link for Docker Image

### Docker Hub Repository
- **Producer Service**: [https://hub.docker.com/r/shakalakahaha/youtube-producer](https://hub.docker.com/r/shakalakahaha/youtube-producer)
- **Consumer Service**: [https://hub.docker.com/r/shakalakahaha/youtube-consumer](https://hub.docker.com/r/shakalakahaha/youtube-consumer)
- **Telegram Bot Service**: [https://hub.docker.com/r/shakalakahaha/youtube-telegram](https://hub.docker.com/r/shakalakahaha/youtube-telegram)

### Docker Images
```bash
# Pull individual images
docker pull shakalakahaha/youtube-producer:latest
docker pull shakalakahaha/youtube-consumer:latest
docker pull shakalakahaha/youtube-telegram:latest
```

### Usage Options
1. **Build from Source** (Recommended for development):
   ```bash
   docker-compose up -d
   ```

2. **Use Pre-built Images** (Faster deployment):
   ```bash
   docker-compose -f docker-compose.hub.yml up -d
   ```

## Instructions on how to run Docker.

## List of all the endpoints

## User manual/guideline for system configuration (if any)

## User manual/guideline for testing the system

## Link for the YouTube Presentation

## Result/Output (Screenshot of the output)

## References (Not less than 20)

## User manual for installing your application on Cloud (Bonus 5%)



