[![Review Assignment Due Date](https://classroom.github.com/assets/deadline-readme-button-22041afd0340ce965d47ae6ef1cefeee28c7c493a6346c4f15d667ab976d596c.svg)](https://classroom.github.com/a/gdOVIhcR)
## Requirements for Group Project
[Read the instruction](https://github.com/STIWK3014-A242/class-activity-stiwk3014/blob/main/GroupProject.md)

## Refer to the link below for the `Group Name` and `Group Members`
https://github.com/STIWK3014-A242/class-activity-stiwk3014/blob/main/NewGroupMembers.md

## Group Info:
1. Matric Number & Name & Photo & Phone Number
1. Mention who the leader is.
1. Mention your group name for Assignment-1 and Assignment-2
1. Other related info (if any)

| Group Member    | #1    | #2    | #3    | #4    | #5    | #6    | #7    |
| :---:   | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
| Name    |  (LEADER) Ng <PERSON><PERSON> |  |  |  |  |  |  |
| Matric Number | 293314 |  |  |  |  |  |  |
| Phone Number | 018-3780532 |  |  |  |  |  |  |
| Picture | <img src="https://github.com/user-attachments/assets/a902ef44-f8df-4017-80e1-b6c21f3ab230" width="100"/> |  |  |  |  |  |  |
| Group Name for Ass1 & Ass2 | nezha1, nezha2 |  |  |  |  |  |  |

## Title of your application (a unique title)
## Abstract (in 300 words)
   1. Background
   2. Problem Statement (from article)
   3. Main objective
   4. Methodology
   5. Result
   6. Conclusion

## System Architecture (MUST be included in your presentation)
![image](https://github.com/user-attachments/assets/4de110b6-db0b-494f-b41c-6e2a881a67f0)

## Link for Docker Image

### Docker Hub Repository
- **Producer Service**: [https://hub.docker.com/r/shakalakahaha/youtube-producer](https://hub.docker.com/r/shakalakahaha/youtube-producer)
- **Consumer Service**: [https://hub.docker.com/r/shakalakahaha/youtube-consumer](https://hub.docker.com/r/shakalakahaha/youtube-consumer)
- **Telegram Bot Service**: [https://hub.docker.com/r/shakalakahaha/youtube-telegram](https://hub.docker.com/r/shakalakahaha/youtube-telegram)

### Docker Images
```bash
# Pull individual images
docker pull shakalakahaha/youtube-producer:latest
docker pull shakalakahaha/youtube-consumer:latest
docker pull shakalakahaha/youtube-telegram:latest
```

### Usage Options
1. **Build from Source** (Recommended for development):
   ```bash
   docker-compose up -d
   ```

2. **Use Pre-built Images** (Faster deployment):
   ```bash
   docker-compose -f docker-compose.hub.yml up -d
   ```

## Instructions on how to run Docker

### Prerequisites
- **Docker Desktop** installed and running
- **Docker Compose** (included with Docker Desktop)
- **Git** for cloning the repository
- **YouTube API Key** (get from [Google Cloud Console](https://console.cloud.google.com/))
- **Telegram Bot Token** (get from [@BotFather](https://t.me/botfather))

### Quick Start (Recommended)

#### Option 1: Build from Source (Development)
```bash
# 1. Clone the repository
git clone <your-repo-url>
cd "rt group project"

# 2. Create environment file
copy env.example .env

# 3. Edit .env file with your API keys
# YOUTUBE_API_KEY=your_youtube_api_key_here
# TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# 4. Build and start all services
docker-compose up -d

# 5. Check if all services are running
docker-compose ps
```

#### Option 2: Use Pre-built Images (Production)
```bash
# 1. Clone the repository
git clone <your-repo-url>
cd "rt group project"

# 2. Create environment file
copy env.example .env

# 3. Edit .env file with your API keys
# YOUTUBE_API_KEY=your_youtube_api_key_here
# TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# 4. Pull and start services using pre-built images
docker-compose -f docker-compose.hub.yml up -d

# 5. Check if all services are running
docker-compose -f docker-compose.hub.yml ps
```

### Detailed Setup Instructions

#### Step 1: Install Docker Desktop
1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop/)
2. Install and start Docker Desktop
3. Verify installation:
   ```bash
   docker --version
   docker-compose --version
   ```

#### Step 2: Get API Keys
1. **YouTube API Key:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing
   - Enable YouTube Data API v3
   - Create credentials (API Key)
   - Copy the API key

2. **Telegram Bot Token:**
   - Message [@BotFather](https://t.me/botfather) on Telegram
   - Send `/newbot` command
   - Follow instructions to create bot
   - Copy the bot token

#### Step 3: Configure Environment
```bash
# Create .env file from template
copy env.example .env

# Edit .env file with your keys:
YOUTUBE_API_KEY=AIzaSyC4Ss8_your_actual_youtube_api_key_here
TELEGRAM_BOT_TOKEN=1234567890:AAEhBOweik6ad2qkN-your_actual_bot_token
```

#### Step 4: Start Services
```bash
# Option A: Build from source (takes longer, always latest code)
docker-compose up -d

# Option B: Use pre-built images (faster, stable release)
docker-compose -f docker-compose.hub.yml up -d
```

### Service Access Points

Once all services are running, access them at:

| Service | URL | Description |
|---------|-----|-------------|
| **Producer Web App** | http://localhost:8081 | Submit YouTube URLs |
| **Analytics Dashboard** | http://localhost:8083 | View real-time analytics |
| **Telegram Bot** | Search `YTCommentBot` | Receive notifications |
| **MySQL Database** | localhost:3309 | Database access |
| **Kafka** | localhost:9092 | Message broker |

### Common Docker Commands

#### Check Service Status
```bash
# View running containers
docker-compose ps

# View logs for all services
docker-compose logs

# View logs for specific service
docker-compose logs producer-app
docker-compose logs consumer-app
docker-compose logs telegram-bot
```

#### Stop and Start Services
```bash
# Stop all services
docker-compose down

# Stop and remove volumes (clean restart)
docker-compose down -v

# Start services in background
docker-compose up -d

# Start services with logs visible
docker-compose up
```

#### Troubleshooting Commands
```bash
# Rebuild specific service
docker-compose build producer-app
docker-compose up -d producer-app

# View container resource usage
docker stats

# Access container shell
docker exec -it youtube-producer bash
docker exec -it youtube-consumer bash
docker exec -it youtube-telegram-bot bash

# Check container health
docker inspect youtube-producer | findstr Health
```

### Port Configuration

| Service | Internal Port | External Port | Purpose |
|---------|---------------|---------------|---------|
| Producer App | 8081 | 8081 | Web interface |
| Consumer App | 8082 | 8083 | Analytics dashboard |
| Telegram Bot | 8084 | 8084 | Bot service |
| MySQL | 3306 | 3309 | Database |
| Kafka | 9092 | 9092 | Message broker |
| Zookeeper | 2181 | 2181 | Kafka coordination |

### Environment Variables

Required environment variables in `.env` file:

```bash
# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Database Configuration (Optional - uses defaults)
DB_HOST=mysql
DB_PORT=3306
DB_NAME=youtube_analytics
DB_USER=youtube_user
DB_PASSWORD=youtube_pass

# Kafka Configuration (Optional - uses defaults)
KAFKA_BOOTSTRAP_SERVERS=kafka:9092
```

### Verification Steps

#### 1. Check All Services are Running
```bash
docker-compose ps
```
Expected output: All services should show "Up" status

#### 2. Test Individual Services
```bash
# Test Producer App
curl http://localhost:8081/api/status

# Test Consumer App
curl http://localhost:8083

# Test Database Connection
docker exec youtube-mysql mysql -u youtube_user -pyoutube_pass -e "SHOW DATABASES;"
```

#### 3. Check Service Logs
```bash
# Check for any errors in logs
docker-compose logs --tail=50

# Monitor real-time logs
docker-compose logs -f
```

### Troubleshooting Guide

#### Common Issues and Solutions

**Issue 1: Port Already in Use**
```bash
# Find process using the port
netstat -ano | findstr :8081

# Kill the process (replace PID with actual process ID)
taskkill /PID <process_id> /F

# Or change ports in docker-compose.yml
```

**Issue 2: Services Not Starting**
```bash
# Check Docker Desktop is running
docker info

# Restart Docker Desktop
# Then try again:
docker-compose down
docker-compose up -d
```

**Issue 3: Database Connection Issues**
```bash
# Reset database
docker-compose down -v
docker-compose up -d mysql
# Wait 30 seconds for MySQL to initialize
docker-compose up -d
```

**Issue 4: Kafka Connection Issues**
```bash
# Restart Kafka services
docker-compose restart zookeeper kafka
# Wait 30 seconds
docker-compose restart producer-app consumer-app telegram-bot
```

**Issue 5: API Key Issues**
```bash
# Verify .env file exists and has correct format
type .env

# Restart services after updating .env
docker-compose down
docker-compose up -d
```

### Performance Optimization

#### For Development
```bash
# Allocate more resources to Docker Desktop
# Settings > Resources > Advanced
# Increase CPU: 4+ cores
# Increase Memory: 8+ GB
```

#### For Production
```bash
# Use pre-built images for faster startup
docker-compose -f docker-compose.hub.yml up -d

# Enable Docker BuildKit for faster builds
set DOCKER_BUILDKIT=1
docker-compose build
```

### Clean Up Commands

```bash
# Stop and remove all containers
docker-compose down

# Remove all containers, networks, and volumes
docker-compose down -v

# Remove all unused Docker resources
docker system prune -a

# Remove specific images
docker rmi shakalakahaha/youtube-producer:latest
docker rmi shakalakahaha/youtube-consumer:latest
docker rmi shakalakahaha/youtube-telegram:latest
```

## List of all the endpoints

## User manual/guideline for system configuration (if any)

## User manual/guideline for testing the system

## Link for the YouTube Presentation

## Result/Output (Screenshot of the output)

## References (Not less than 20)

## User manual for installing your application on Cloud (Bonus 5%)



