package com.youtube.telegram.service;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
public class ControlMessageConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(ControlMessageConsumerService.class);

    private final YouTubeAnalyticsBot youTubeAnalyticsBot;
    private final VideoCommentBatcher videoCommentBatcher;
    private final KafkaOffsetResetService kafkaOffsetResetService;

    public ControlMessageConsumerService(YouTubeAnalyticsBot youTubeAnalyticsBot,
                                       VideoCommentBatcher videoCommentBatcher,
                                       KafkaOffsetResetService kafkaOffsetResetService) {
        this.youTubeAnalyticsBot = youTubeAnalyticsBot;
        this.videoCommentBatcher = videoCommentBatcher;
        this.kafkaOffsetResetService = kafkaOffsetResetService;
    }

    /**
     * Listen for control messages from producer app
     */
    @KafkaListener(topics = "youtube-control", groupId = "youtube-telegram-control-group-v3")
    public void handleControlMessage(Map<String, String> message) {
        logger.info("Received control message: {}", message);

        try {
            String action = message.get("action");
            String processingSession = message.get("processingSession");
            
            if ("clear".equals(action)) {
                logger.info("New processing session started: {}. Performing complete reset and notifying Telegram subscribers.", processingSession);

                // STEP 1: Reset Kafka consumer offsets to ignore old messages
                kafkaOffsetResetService.resetTelegramConsumerOffsets();

                // STEP 2: Clear all video batches and reset counters
                videoCommentBatcher.clearAll();

                // STEP 3: Reset bot counters for new session
                youTubeAnalyticsBot.resetCounters();

                // STEP 4: Notify subscribers about new processing
                youTubeAnalyticsBot.notifyProcessingStarted(processingSession, 5); // 5 videos expected

                logger.info("Successfully performed complete reset and notified subscribers about new processing session: {}", processingSession);
            } else {
                logger.warn("Unknown control action received: {}", action);
            }

        } catch (Exception e) {
            logger.error("Error processing control message: {}", message, e);
        }
    }
} 