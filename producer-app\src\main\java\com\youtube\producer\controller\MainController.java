package com.youtube.producer.controller;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.youtube.producer.service.DatabaseClearingService;
import com.youtube.producer.service.YoutubeLinkService;

@Controller
public class MainController {

    private static final Logger logger = LoggerFactory.getLogger(MainController.class);

    private final YoutubeLinkService youtubeLinkService;
    private final DatabaseClearingService databaseClearingService;

    public MainController(YoutubeLinkService youtubeLinkService,
                         DatabaseClearingService databaseClearingService) {
        this.youtubeLinkService = youtubeLinkService;
        this.databaseClearingService = databaseClearingService;
    }

    /**
     * Home page - displays the form and current status
     */
    @GetMapping("/")
    public String home(Model model) {
        logger.debug("Displaying home page");

        // Get current stats
        YoutubeLinkService.SubmissionStats stats = youtubeLinkService.getStats();

        // Add data to model
        model.addAttribute("stats", stats);

        // Initialize empty form data
        List<String> emptyUrls = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            emptyUrls.add("");
        }
        model.addAttribute("videoUrls", emptyUrls);

        return "index";
    }

    /**
     * Handle form submission
     */
    @PostMapping("/submit")
    public String submitLinks(@RequestParam("videoUrls") List<String> videoUrls,
                             RedirectAttributes redirectAttributes) {
        logger.info("Received link submission with {} URLs", videoUrls.size());

        try {
            // Submit the links
            YoutubeLinkService.SubmissionResult result = youtubeLinkService.submitLinks(videoUrls);

            if (result.isSuccess()) {
                redirectAttributes.addFlashAttribute("successMessage", 
                    "Successfully submitted " + result.getSavedLinks().size() + " YouTube links! " +
                    "The system will start fetching data every 30 seconds.");
                
                logger.info("Successfully submitted {} YouTube links", result.getSavedLinks().size());
            } else {
                redirectAttributes.addFlashAttribute("errorMessages", result.getErrors());
                redirectAttributes.addFlashAttribute("videoUrls", videoUrls); // Preserve form data
                
                logger.warn("Link submission failed with {} errors", result.getErrors().size());
            }

        } catch (Exception e) {
            logger.error("Error during link submission", e);
            redirectAttributes.addFlashAttribute("errorMessage", 
                "An unexpected error occurred: " + e.getMessage());
            redirectAttributes.addFlashAttribute("videoUrls", videoUrls); // Preserve form data
        }

        return "redirect:/";
    }

    /**
     * Clear all links
     */
    @PostMapping("/clear")
    public String clearLinks(RedirectAttributes redirectAttributes) {
        logger.info("Clearing all YouTube links");

        try {
            databaseClearingService.clearAllLinks();
            redirectAttributes.addFlashAttribute("successMessage", "All YouTube links have been cleared.");
        } catch (Exception e) {
            logger.error("Error clearing links", e);
            redirectAttributes.addFlashAttribute("errorMessage", "Error clearing links: " + e.getMessage());
        }

        return "redirect:/";
    }



    /**
     * API endpoint for checking status (JSON response)
     */
    @GetMapping("/api/status")
    @ResponseBody
    public StatusResponse getStatus() {
        YoutubeLinkService.SubmissionStats stats = youtubeLinkService.getStats();

        return new StatusResponse(
            stats.getActiveLinks(),
            stats.getTotalLinks(),
            "Every 30 seconds",
            "Running"
        );
    }

    /**
     * Get submission statistics
     */
    @GetMapping("/api/stats")
    @ResponseBody
    public YoutubeLinkService.SubmissionStats getStats() {
        return youtubeLinkService.getStats();
    }





    /**
     * Status response for API endpoint
     */
    public static class StatusResponse {
        private final long activeLinks;
        private final long totalLinks;
        private final String schedulerInterval;
        private final String status;

        public StatusResponse(long activeLinks, long totalLinks, String schedulerInterval, String status) {
            this.activeLinks = activeLinks;
            this.totalLinks = totalLinks;
            this.schedulerInterval = schedulerInterval;
            this.status = status;
        }

        public long getActiveLinks() { return activeLinks; }
        public long getTotalLinks() { return totalLinks; }
        public String getSchedulerInterval() { return schedulerInterval; }
        public String getStatus() { return status; }
    }
} 