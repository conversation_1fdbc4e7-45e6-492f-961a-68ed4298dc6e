package com.youtube.consumer.service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.youtube.shared.model.ChannelMetadata;

@Service
public class AnalyticsService {

    private static final Logger logger = LoggerFactory.getLogger(AnalyticsService.class);

    private final ChannelMetadataConsumerService channelMetadataConsumerService;

    public AnalyticsService(ChannelMetadataConsumerService channelMetadataConsumerService) {
        this.channelMetadataConsumerService = channelMetadataConsumerService;
    }

    /**
     * Calculate analytics based on unique channels (deduplication)
     * Multiple videos from same channel = 1 channel in analytics
     */
    public Map<String, Object> calculateAnalytics() {
        logger.info("Starting analytics calculation...");
        
        List<ChannelMetadata> uniqueChannels = channelMetadataConsumerService.getAllChannelMetadata();
        
        if (uniqueChannels.isEmpty()) {
            logger.warn("No channel metadata available for analytics");
            return Map.of(
                "status", "No data available",
                "totalChannels", 0,
                "totalVideos", 0,
                "analytics", Map.of()
            );
        }

        Map<String, Object> analytics = new HashMap<>();
        
        // Basic statistics
        int totalUniqueChannels = uniqueChannels.size();
        int totalVideos = channelMetadataConsumerService.getStats().get("totalVideos").toString() != null ? 
                         Integer.parseInt(channelMetadataConsumerService.getStats().get("totalVideos").toString()) : 0;
        
        logger.info("Calculating analytics for {} unique channels and {} total videos", totalUniqueChannels, totalVideos);

        // Calculate aggregate metrics across all unique channels
        long totalSubscribers = 0;
        long totalChannelVideos = 0;
        long totalChannelComments = 0;
        long totalChannelLikes = 0;
        
        for (ChannelMetadata channel : uniqueChannels) {
            totalSubscribers += channel.getSubscribersCount() != null ? channel.getSubscribersCount() : 0;
            totalChannelVideos += channel.getTotalVideos() != null ? channel.getTotalVideos() : 0;
            totalChannelComments += channel.getTotalComments() != null ? channel.getTotalComments() : 0;
            totalChannelLikes += channel.getTotalLikes() != null ? channel.getTotalLikes() : 0;
        }

        // 1. Engagement Rate Calculation: (Likes + Comments) / Subscribers × 100
        double engagementRate = 0.0;
        if (totalSubscribers > 0) {
            engagementRate = ((double) (totalChannelLikes + totalChannelComments) / totalSubscribers) * 100;
        }

        // 2. Average Likes per Video: Total Likes / Total Videos
        double avgLikesPerVideo = 0.0;
        if (totalChannelVideos > 0) {
            avgLikesPerVideo = (double) totalChannelLikes / totalChannelVideos;
        }

        // Additional analytics
        double avgSubscribersPerChannel = totalUniqueChannels > 0 ? (double) totalSubscribers / totalUniqueChannels : 0;
        double avgVideosPerChannel = totalUniqueChannels > 0 ? (double) totalChannelVideos / totalUniqueChannels : 0;
        double avgCommentsPerChannel = totalUniqueChannels > 0 ? (double) totalChannelComments / totalUniqueChannels : 0;

        // Build analytics result
        analytics.put("totalUniqueChannels", totalUniqueChannels);
        analytics.put("totalVideosSubmitted", totalVideos);
        analytics.put("totalChannelVideos", totalChannelVideos);
        analytics.put("totalSubscribers", totalSubscribers);
        analytics.put("totalChannelComments", totalChannelComments);
        analytics.put("totalChannelLikes", totalChannelLikes);
        
        // Key metrics
        analytics.put("engagementRate", Math.round(engagementRate * 100.0) / 100.0);
        analytics.put("avgLikesPerVideo", Math.round(avgLikesPerVideo * 100.0) / 100.0);
        analytics.put("avgSubscribersPerChannel", Math.round(avgSubscribersPerChannel));
        analytics.put("avgVideosPerChannel", Math.round(avgVideosPerChannel * 100.0) / 100.0);
        analytics.put("avgCommentsPerChannel", Math.round(avgCommentsPerChannel));
        
        // Channel breakdown
        analytics.put("channelBreakdown", getChannelBreakdown(uniqueChannels));
        
        // NEW: Channel Comparisons
        analytics.put("channelComparisons", calculateChannelComparisons(uniqueChannels));

        logger.info("Analytics calculation completed. Engagement rate: {}%, Avg likes per video: {}", 
                   Math.round(engagementRate * 100.0) / 100.0, Math.round(avgLikesPerVideo * 100.0) / 100.0);

        return Map.of(
            "status", "success",
            "totalChannels", totalUniqueChannels,
            "totalVideos", totalVideos,
            "analytics", analytics
        );
    }

    /**
     * Calculate channel comparisons - find channels with highest metrics
     */
    private Map<String, Object> calculateChannelComparisons(List<ChannelMetadata> channels) {
        logger.info("Calculating channel comparisons for {} channels", channels.size());
        
        Map<String, Object> comparisons = new HashMap<>();
        
        // 1. Channel with most subscribers
        Optional<ChannelMetadata> mostSubscribers = channels.stream()
            .max(Comparator.comparing(channel -> 
                channel.getSubscribersCount() != null ? channel.getSubscribersCount() : 0L));
        
        // 2. Channel with most videos
        Optional<ChannelMetadata> mostVideos = channels.stream()
            .max(Comparator.comparing(channel -> 
                channel.getTotalVideos() != null ? channel.getTotalVideos() : 0L));
        
        // 3. Channel with most comments
        Optional<ChannelMetadata> mostComments = channels.stream()
            .max(Comparator.comparing(channel -> 
                channel.getTotalComments() != null ? channel.getTotalComments() : 0L));
        
        // 4. Channel with most likes
        Optional<ChannelMetadata> mostLikes = channels.stream()
            .max(Comparator.comparing(channel -> 
                channel.getTotalLikes() != null ? channel.getTotalLikes() : 0L));
        
        // 5. Channel with highest average views per video (using actual view count)
        Optional<ChannelMetadata> highestAvgViews = channels.stream()
            .max(Comparator.comparing(channel -> {
                if (channel.getTotalVideos() == null || channel.getTotalVideos() == 0) {
                    return 0.0;
                }
                long views = channel.getTotalViews() != null ? channel.getTotalViews() : 0;
                return (double) views / channel.getTotalVideos();
            }));
        
        // 6. Channel with best engagement rate
        Optional<ChannelMetadata> bestEngagement = channels.stream()
            .max(Comparator.comparing(channel -> {
                if (channel.getSubscribersCount() == null || channel.getSubscribersCount() == 0) {
                    return 0.0;
                }
                long likes = channel.getTotalLikes() != null ? channel.getTotalLikes() : 0;
                long comments = channel.getTotalComments() != null ? channel.getTotalComments() : 0;
                return ((double) (likes + comments) / channel.getSubscribersCount()) * 100;
            }));
        
        // Build comparison results
        comparisons.put("mostSubscribers", createComparisonResult(mostSubscribers, "subscribers"));
        comparisons.put("mostVideos", createComparisonResult(mostVideos, "videos"));
        comparisons.put("mostComments", createComparisonResult(mostComments, "comments"));
        comparisons.put("mostLikes", createComparisonResult(mostLikes, "likes"));
        comparisons.put("highestAvgViews", createComparisonResult(highestAvgViews, "avgViews"));
        comparisons.put("bestEngagement", createComparisonResult(bestEngagement, "engagement"));
        
        logger.info("Channel comparisons calculated successfully");
        return comparisons;
    }
    
    /**
     * Create a standardized comparison result
     */
    private Map<String, Object> createComparisonResult(Optional<ChannelMetadata> channelOpt, String metric) {
        if (channelOpt.isEmpty()) {
            return Map.of(
                "status", "No data available",
                "metric", metric
            );
        }
        
        ChannelMetadata channel = channelOpt.get();
        Map<String, Object> result = new HashMap<>();
        
        result.put("channelName", channel.getChannelName());
        result.put("channelId", channel.getChannelId());
        result.put("metric", metric);
        
        switch (metric) {
            case "subscribers":
                result.put("value", channel.getSubscribersCount() != null ? channel.getSubscribersCount() : 0);
                result.put("formattedValue", formatNumber(channel.getSubscribersCount()));
                result.put("description", "subscribers");
                break;
            case "videos":
                result.put("value", channel.getTotalVideos() != null ? channel.getTotalVideos() : 0);
                result.put("formattedValue", formatNumber(channel.getTotalVideos()));
                result.put("description", "total videos");
                break;
            case "comments":
                result.put("value", channel.getTotalComments() != null ? channel.getTotalComments() : 0);
                result.put("formattedValue", formatNumber(channel.getTotalComments()));
                result.put("description", "total comments");
                break;
            case "likes":
                result.put("value", channel.getTotalLikes() != null ? channel.getTotalLikes() : 0);
                result.put("formattedValue", formatNumber(channel.getTotalLikes()));
                result.put("description", "total likes");
                break;
            case "avgViews":
                double avgViews = 0.0;
                if (channel.getTotalVideos() != null && channel.getTotalVideos() > 0) {
                    long views = channel.getTotalViews() != null ? channel.getTotalViews() : 0;
                    avgViews = (double) views / channel.getTotalVideos();
                }
                result.put("value", Math.round(avgViews * 100.0) / 100.0);
                result.put("formattedValue", formatNumber((long) avgViews));
                result.put("description", "average views per video");
                break;
            case "engagement":
                double engagementRate = 0.0;
                if (channel.getSubscribersCount() != null && channel.getSubscribersCount() > 0) {
                    long likes = channel.getTotalLikes() != null ? channel.getTotalLikes() : 0;
                    long comments = channel.getTotalComments() != null ? channel.getTotalComments() : 0;
                    engagementRate = ((double) (likes + comments) / channel.getSubscribersCount()) * 100;
                }
                result.put("value", Math.round(engagementRate * 100.0) / 100.0);
                result.put("formattedValue", String.format("%.2f%%", engagementRate));
                result.put("description", "engagement rate");
                break;
        }
        
        // Additional channel info
        result.put("subscribersCount", channel.getSubscribersCount() != null ? channel.getSubscribersCount() : 0);
        result.put("totalVideos", channel.getTotalVideos() != null ? channel.getTotalVideos() : 0);
        result.put("videosSubmitted", channelMetadataConsumerService.getVideoCountForChannel(channel.getChannelId()));
        
        return result;
    }
    
    /**
     * Format large numbers with appropriate suffixes (K, M, B)
     */
    private String formatNumber(Long number) {
        if (number == null) return "0";
        
        long num = number;
        if (num >= 1_000_000_000) {
            return String.format("%.1fB", num / 1_000_000_000.0);
        } else if (num >= 1_000_000) {
            return String.format("%.1fM", num / 1_000_000.0);
        } else if (num >= 1_000) {
            return String.format("%.1fK", num / 1_000.0);
        } else {
            return String.valueOf(num);
        }
    }

    /**
     * Get detailed breakdown per channel
     */
    private List<Map<String, Object>> getChannelBreakdown(List<ChannelMetadata> channels) {
        return channels.stream().map(channel -> {
            int videosFromThisChannel = channelMetadataConsumerService.getVideoCountForChannel(channel.getChannelId());
            
            double channelEngagementRate = 0.0;
            if (channel.getSubscribersCount() != null && channel.getSubscribersCount() > 0) {
                long channelLikes = channel.getTotalLikes() != null ? channel.getTotalLikes() : 0;
                long channelComments = channel.getTotalComments() != null ? channel.getTotalComments() : 0;
                channelEngagementRate = ((double) (channelLikes + channelComments) / channel.getSubscribersCount()) * 100;
            }
            
            double channelAvgLikesPerVideo = 0.0;
            if (channel.getTotalVideos() != null && channel.getTotalVideos() > 0) {
                long channelLikes = channel.getTotalLikes() != null ? channel.getTotalLikes() : 0;
                channelAvgLikesPerVideo = (double) channelLikes / channel.getTotalVideos();
            }

            return Map.of(
                "channelName", channel.getChannelName(),
                "channelId", channel.getChannelId(),
                "subscribersCount", channel.getSubscribersCount() != null ? channel.getSubscribersCount() : 0,
                "totalVideos", channel.getTotalVideos() != null ? channel.getTotalVideos() : 0,
                "totalComments", channel.getTotalComments() != null ? channel.getTotalComments() : 0,
                "totalLikes", channel.getTotalLikes() != null ? channel.getTotalLikes() : 0,
                "videosSubmitted", videosFromThisChannel,
                "engagementRate", Math.round(channelEngagementRate * 100.0) / 100.0,
                "avgLikesPerVideo", Math.round(channelAvgLikesPerVideo * 100.0) / 100.0,
                "submittedVideoIds", channelMetadataConsumerService.getVideosForChannel(channel.getChannelId())
            );
        }).toList();
    }

    /**
     * Get current analytics status
     */
    public Map<String, Object> getAnalyticsStatus() {
        Map<String, Object> stats = channelMetadataConsumerService.getStats();
        return Map.of(
            "ready", (Integer) stats.get("totalChannels") > 0,
            "dataReceived", stats.get("newDataReceived"),
            "summary", stats
        );
    }
} 