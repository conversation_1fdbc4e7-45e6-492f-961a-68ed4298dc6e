package com.youtube.producer.service;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.youtube.producer.entity.YoutubeLink;
import com.youtube.producer.repository.YoutubeLinkRepository;
import com.youtube.shared.model.CommentData;

@Service
public class YoutubeSchedulerService {

    private static final Logger logger = LoggerFactory.getLogger(YoutubeSchedulerService.class);

    private final YoutubeLinkRepository youtubeLinkRepository;
    private final YoutubeApiService youtubeApiService;
    private final KafkaProducerService kafkaProducerService;
    private final YoutubeLinkService youtubeLinkService;

    public YoutubeSchedulerService(YoutubeLinkRepository youtubeLinkRepository,
                                 YoutubeApiService youtubeApiService,
                                 KafkaProducerService kafkaProducerService,
                                 YoutubeLinkService youtubeLinkService) {
        this.youtubeLinkRepository = youtubeLinkRepository;
        this.youtubeApiService = youtubeApiService;
        this.kafkaProducerService = kafkaProducerService;
        this.youtubeLinkService = youtubeLinkService;
        
        logger.info("YouTubeSchedulerService initialized - following README.md flow");
    }

    /**
     * Scheduled task that runs every 30 seconds
     * Simple flow as per README.md:
     * 1. Fetch each video link from the database
     * 2. Query YouTube API for channel info + top 5 most liked comments
     * 3. Send data to Kafka for processing
     */
    @Scheduled(fixedDelayString = "${scheduler.youtube.fetch-interval}")
    public void fetchYoutubeData() {
        logger.debug("Starting scheduled YouTube data fetch (README.md flow)...");

        try {
            // Step 1: Fetch each video link from the database
            List<YoutubeLink> activeLinks = youtubeLinkRepository.findByIsActiveTrue();
            
            if (activeLinks.isEmpty()) {
                logger.debug("No active YouTube links found in database");
                return;
            }

            logger.info("Found {} active YouTube links to fetch data for", activeLinks.size());

            // Step 2 & 3: Query YouTube API and send to Kafka for each link
            int processedCount = 0;
            for (YoutubeLink link : activeLinks) {
                if (fetchAndProcessVideoData(link)) {
                    processedCount++;
                }
            }

            if (processedCount > 0) {
                logger.info("Successfully fetched and processed data for {} videos", processedCount);
            }

        } catch (Exception e) {
            logger.error("Error during scheduled YouTube data fetch", e);
        }
    }

    /**
     * Fetch YouTube data for a single video and send to Kafka
     * As per README.md: Get channel info + top 5 most liked comments
     */
    private boolean fetchAndProcessVideoData(YoutubeLink link) {
        try {
            logger.debug("Fetching YouTube data for: {}", link.getVideoUrl());

            // Query YouTube Data API for channel info + top 5 most liked comments
            YoutubeApiService.VideoData videoData = youtubeApiService.fetchVideoData(link.getVideoId());
            
            if (videoData == null) {
                logger.warn("No video data found for: {}", link.getVideoUrl());
                return false;
            }

            List<CommentData> allComments = videoData.getNewestComments();

            // Filter out already processed comments
            List<CommentData> newComments = filterNewComments(allComments, link.getLastProcessedCommentId());

            // Log what we fetched
            logger.info("Fetched {} total comments for '{}' from channel '{}' - {} are new",
                       allComments.size(),
                       videoData.getVideoTitle(),
                       videoData.getChannelMetadata().getChannelName(),
                       newComments.size());

            // Only process if there are new comments
            if (!newComments.isEmpty()) {
                // Step 3: Package the data and send to Kafka
                kafkaProducerService.processComments(
                    link.getVideoUrl(),
                    videoData.getChannelMetadata(),
                    newComments
                );

                // Update the last processed comment ID (newest comment is first)
                String latestCommentId = allComments.get(0).getCommentId();
                youtubeLinkService.updateLastProcessedCommentId(link.getVideoId(), latestCommentId);
            } else {
                logger.debug("No new comments found for video: {}", link.getVideoUrl());
            }

            logger.debug("Successfully processed YouTube data for: {}", link.getVideoUrl());
            return true;

        } catch (Exception e) {
            logger.error("Error fetching YouTube data for: {}", link.getVideoUrl(), e);
            return false;
        }
    }

    /**
     * Filter out comments that have already been processed
     * Returns only new comments that haven't been seen before
     */
    private List<CommentData> filterNewComments(List<CommentData> allComments, String lastProcessedCommentId) {
        List<CommentData> newComments = new ArrayList<>();

        // If no last processed comment ID, all comments are new
        if (lastProcessedCommentId == null || lastProcessedCommentId.isEmpty()) {
            return allComments;
        }

        // Find comments that are newer than the last processed one
        for (CommentData comment : allComments) {
            if (comment.getCommentId().equals(lastProcessedCommentId)) {
                // Found the last processed comment, stop here
                break;
            }
            newComments.add(comment);
        }

        return newComments;
    }

    /**
     * Manual trigger for testing purposes
     */
    public void triggerManualFetch() {
        logger.info("Manual YouTube data fetch triggered");
        fetchYoutubeData();
    }

    /**
     * Get statistics about the scheduled task
     */
    public SchedulerStats getStats() {
        long activeLinksCount = youtubeLinkRepository.countActiveLinks();
        return new SchedulerStats(activeLinksCount);
    }

    /**
     * Statistics class for scheduler information
     */
    public static class SchedulerStats {
        private final long activeLinksCount;

        public SchedulerStats(long activeLinksCount) {
            this.activeLinksCount = activeLinksCount;
        }

        public long getActiveLinksCount() {
            return activeLinksCount;
        }

        public String getIntervalDescription() {
            if (activeLinksCount > 0) {
                return "Every 30 seconds (" + activeLinksCount + " active videos)";
            } else {
                return "Idle (no active videos)";
            }
        }
    }
} 