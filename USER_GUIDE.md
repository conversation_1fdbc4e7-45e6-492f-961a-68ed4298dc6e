# 🚀 YouTube Comment System - User Guide

## 📋 Prerequisites

1. **Docker & Docker Compose** installed on your system

## 🔧 System Startup Guide

### Step 1: Start the System

Open terminal in the project root directory and run:

```bash
# Build and start all services
docker-compose up --build -d
```

**⏱️ Wait 2-3 minutes** for all services to fully start up.

### Step 2: Verify System is Running

```bash
# Check if all containers are running
docker-compose ps
```

**Expected:** All services should show "Up (healthy)" status.

### Step 3: Check System Health

```bash
# Test if apps are working
curl http://localhost:8081  # Producer App
curl http://localhost:8083  # Consumer App
```

**Expected:** Both should return HTML pages without errors.

## 🎯 How to Use the System

### 1. Setup Telegram Bot (First Time Only)

**Step 1.1: Find the Bot**
- Open Telegram on your phone/desktop
- Search for: **YTCommentBot**
- Start a chat with the bot

**Step 1.2: Initialize Bot**
```
/start
```
**Expected Response:** Welcome message from YouTube Comment Bot

**Step 1.3: Subscribe to Notifications**
```
/subscribe
```
**Expected Response:** "You have been subscribed to receive YouTube comment notifications!"

### 2. Submit YouTube Videos

- Open browser: **http://localhost:8081**
- Enter exactly **5 YouTube video URLs** in the form
- Click "Submit Links"
- Page will show "Active Video Links: 5"

### 3. View Analytics

- Open new browser tab: **http://localhost:8083**
- Wait 30-60 seconds for data to appear
- Click "Calculate Analytics" to see video comparison
- Click "X" button to close analytics when done

### 4. Receive Telegram Notifications

**What You'll Get:**
- Odd-length comments from your 5 videos
- Real-time notifications every 30 seconds
- Rich formatting with video details

**Sample Message:**
```
🎬 New Comments from Video 1/5

📺 Channel: Example Channel
🎥 Video: "Amazing Video Title"
👀 Views: 1,234,567 | 👍 Likes: 12,345

💬 New Comments:
• "Great video!" (11 chars)
• "Amazing content!" (17 chars)
```

## 🔄 How It Works

### Automatic Processing (Every 30 seconds)
1. System fetches 5 newest comments from each video
2. **Odd-length comments** → Sent to Telegram
3. **Even-length comments** → Update analytics data
4. Process repeats automatically

### Fresh Start
- **New videos**: Clears all previous data
- **System restart**: Complete reset, no old comments

## 🛠️ Basic Commands

### Start/Stop System
```bash
# Start system
docker-compose up --build -d

# Check status
docker-compose ps

# Stop system
docker-compose down

# Fresh restart
docker-compose down -v && docker-compose up --build -d
```

### View Logs
```bash
# View all logs
docker-compose logs -f

# View specific service
docker-compose logs telegram-bot
```

## 🌐 Access URLs

- **Producer App**: http://localhost:8081 (Submit videos)
- **Consumer App**: http://localhost:8083 (View analytics)
- **Telegram Bot**: Search **YTCommentBot** on Telegram

## 🔍 Troubleshooting

### If nothing works:
```bash
# Check logs for errors
docker-compose logs | grep -i error

# Fresh restart
docker-compose down -v && docker-compose up --build -d
```

### If no data appears:
- Wait 2-3 minutes after starting system
- Check if all services show "healthy" status: `docker-compose ps`
- Restart if needed: `docker-compose restart`

## 🎉 What Success Looks Like

### ✅ System Working Properly
- **All services**: Show "Up (healthy)" in `docker-compose ps`
- **Producer App**: Shows "Active Video Links: 5" after submission
- **Consumer App**: Displays video data within 60 seconds
- **Telegram Bot**: Sends odd-length comments automatically

### ✅ Fresh Start Working
- **After restart**: No old comments in Telegram
- **New videos**: Consumer app shows only new videos
- **Clean slate**: System resets completely

## 📝 Important Notes

- **First startup**: Takes 2-3 minutes
- **Processing**: Happens every 30 seconds automatically
- **Fresh start**: Each restart clears all previous data
- **Only new comments**: No duplicates or spam
