package com.youtube.producer.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.youtube.producer.repository.YoutubeLinkRepository;

@Service
public class DatabaseClearingService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseClearingService.class);

    private final YoutubeLinkRepository youtubeLinkRepository;

    public DatabaseClearingService(YoutubeLinkRepository youtubeLinkRepository) {
        this.youtubeLinkRepository = youtubeLinkRepository;
    }

    /**
     * Clear all existing links in a separate transaction to avoid rollback issues
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void clearAllLinks() {
        logger.info("Clearing all existing YouTube links in new transaction");
        long deletedCount = youtubeLinkRepository.count();
        youtubeLinkRepository.deleteAll();
        logger.info("Deleted {} existing YouTube links", deletedCount);
    }
} 