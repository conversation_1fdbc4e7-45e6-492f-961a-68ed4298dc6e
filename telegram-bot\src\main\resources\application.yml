spring:
  application:
    name: youtube-telegram-bot

  # Import centralized Kafka configuration
  config:
    import:
      - optional:file:./kafka-config/application-kafka.yml

  # Database configuration
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:development}
    include: telegram-kafka

  # Database configuration for docker environment
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:youtube_analytics}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC
    username: ${DB_USER:youtube_user}
    password: ${DB_PASSWORD:youtube_pass}
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA configuration
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # Kafka configuration (now centralized in kafka-config/application-kafka.yml)
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    consumer:
      group-id: youtube-telegram-group-v3
      auto-offset-reset: latest
      enable-auto-commit: true
      auto-commit-interval: 1000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
        spring.json.default.type: "com.youtube.shared.model.CommentData"
        spring.json.use.type.headers: false
        spring.json.value.default.type: "com.youtube.shared.model.CommentData"

# Server configuration
server:
  port: 8084

# Telegram Bot configuration
telegram:
  bot:
    token: ${TELEGRAM_BOT_TOKEN:**********************************************}
    username: ${TELEGRAM_BOT_USERNAME:YouTubeAnalyticsBot}
    chat-id: ${TELEGRAM_CHAT_ID:@youtube_analytics_test}

# Logging configuration
logging:
  level:
    com.youtube.telegram: DEBUG
    org.springframework.kafka: INFO
    org.telegram: INFO 