package com.youtube.consumer.model;

import java.util.List;

import com.youtube.shared.model.ChannelMetadata;
import com.youtube.shared.model.CommentData;

/**
 * Data model for displaying video information with its even comments in the consumer app
 */
public class VideoDisplayData {
    private String videoId;
    private String channelName;
    private Long subscribersCount;
    private Long totalVideos;
    private Long totalComments; // Comments for this specific video
    private Long totalLikes;    // Likes for this specific video
    private String evenComment; // The even comment text for this video (first comment for backward compatibility)
    private List<CommentData> evenComments; // All even comments for this video
    private String videoTitle;
    private String videoThumbnail;
    private String videoUrl;

    // Default constructor
    public VideoDisplayData() {}

    // Constructor from ChannelMetadata and CommentData
    public VideoDisplayData(ChannelMetadata metadata, CommentData evenCommentData) {
        this.videoId = metadata.getVideoId();
        this.channelName = metadata.getChannelName();
        this.subscribersCount = metadata.getSubscribersCount();
        this.totalVideos = metadata.getTotalVideos();
        this.totalComments = metadata.getTotalComments();
        this.totalLikes = metadata.getTotalLikes();
        
        if (evenCommentData != null) {
            this.evenComment = evenCommentData.getCommentText();
            this.videoTitle = evenCommentData.getVideoTitle();
            this.videoThumbnail = evenCommentData.getVideoThumbnail();
            this.videoUrl = evenCommentData.getVideoUrl();
        }
    }

    // Constructor with just metadata (no even comment available)
    public VideoDisplayData(ChannelMetadata metadata) {
        this(metadata, (CommentData) null);
    }

    // Constructor from ChannelMetadata and List of CommentData
    public VideoDisplayData(ChannelMetadata metadata, List<CommentData> evenCommentsList) {
        this.videoId = metadata.getVideoId();
        this.channelName = metadata.getChannelName();
        this.subscribersCount = metadata.getSubscribersCount();
        this.totalVideos = metadata.getTotalVideos();
        this.totalComments = metadata.getTotalComments();
        this.totalLikes = metadata.getTotalLikes();

        this.evenComments = evenCommentsList != null ? new java.util.ArrayList<>(evenCommentsList) : new java.util.ArrayList<>();

        if (evenCommentsList != null && !evenCommentsList.isEmpty()) {
            // Set the first comment for backward compatibility
            CommentData firstComment = evenCommentsList.get(0);
            this.evenComment = firstComment.getCommentText();
            this.videoTitle = firstComment.getVideoTitle();
            this.videoThumbnail = firstComment.getVideoThumbnail();
            this.videoUrl = firstComment.getVideoUrl();
        }
    }

    // Getters and Setters
    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Long getSubscribersCount() {
        return subscribersCount;
    }

    public void setSubscribersCount(Long subscribersCount) {
        this.subscribersCount = subscribersCount;
    }

    public Long getTotalVideos() {
        return totalVideos;
    }

    public void setTotalVideos(Long totalVideos) {
        this.totalVideos = totalVideos;
    }

    public Long getTotalComments() {
        return totalComments;
    }

    public void setTotalComments(Long totalComments) {
        this.totalComments = totalComments;
    }

    public Long getTotalLikes() {
        return totalLikes;
    }

    public void setTotalLikes(Long totalLikes) {
        this.totalLikes = totalLikes;
    }

    public String getEvenComment() {
        return evenComment;
    }

    public void setEvenComment(String evenComment) {
        this.evenComment = evenComment;
    }

    public String getVideoTitle() {
        return videoTitle;
    }

    public void setVideoTitle(String videoTitle) {
        this.videoTitle = videoTitle;
    }

    public String getVideoThumbnail() {
        return videoThumbnail;
    }

    public void setVideoThumbnail(String videoThumbnail) {
        this.videoThumbnail = videoThumbnail;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    // Utility methods for formatting
    public String getFormattedSubscribers() {
        if (subscribersCount == null) return "0";
        if (subscribersCount >= 1_000_000) {
            return String.format("%.1fM", subscribersCount / 1_000_000.0);
        } else if (subscribersCount >= 1_000) {
            return String.format("%.1fK", subscribersCount / 1_000.0);
        }
        return subscribersCount.toString();
    }

    public String getFormattedTotalVideos() {
        return totalVideos != null ? totalVideos.toString() : "0";
    }

    public String getFormattedTotalComments() {
        return totalComments != null ? totalComments.toString() : "0";
    }

    public String getFormattedTotalLikes() {
        if (totalLikes == null) return "0";
        if (totalLikes >= 1_000_000) {
            return String.format("%.1fM", totalLikes / 1_000_000.0);
        } else if (totalLikes >= 1_000) {
            return String.format("%.1fK", totalLikes / 1_000.0);
        }
        return totalLikes.toString();
    }

    public List<CommentData> getEvenComments() {
        return evenComments;
    }

    public void setEvenComments(List<CommentData> evenComments) {
        this.evenComments = evenComments;
    }

    public boolean hasEvenComment() {
        return evenComment != null && !evenComment.trim().isEmpty();
    }

    public boolean hasEvenComments() {
        return evenComments != null && !evenComments.isEmpty();
    }

    public int getEvenCommentsCount() {
        return evenComments != null ? evenComments.size() : 0;
    }

    @Override
    public String toString() {
        return "VideoDisplayData{" +
                "videoId='" + videoId + '\'' +
                ", channelName='" + channelName + '\'' +
                ", subscribersCount=" + subscribersCount +
                ", totalVideos=" + totalVideos +
                ", totalComments=" + totalComments +
                ", totalLikes=" + totalLikes +
                ", evenComment='" + (evenComment != null ? evenComment.substring(0, Math.min(50, evenComment.length())) + "..." : "null") + '\'' +
                ", videoTitle='" + videoTitle + '\'' +
                '}';
    }
}
