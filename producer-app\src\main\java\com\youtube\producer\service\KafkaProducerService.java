package com.youtube.producer.service;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import com.youtube.shared.model.ChannelMetadata;
import com.youtube.shared.model.CommentData;

@Service
public class KafkaProducerService {

    private static final Logger logger = LoggerFactory.getLogger(KafkaProducerService.class);
    
    // Kafka topics
    private static final String ODD_COMMENTS_TOPIC = "youtube-odd-comments";
    private static final String EVEN_METADATA_TOPIC = "youtube-even-metadata";
    private static final String EVEN_COMMENTS_TOPIC = "youtube-even-comments";
    private static final String CONTROL_TOPIC = "youtube-control";

    private final KafkaTemplate<String, Object> kafkaTemplate;

    public KafkaProducerService(KafkaTemplate<String, Object> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    /**
     * Process comments and send them to appropriate Kafka topics based on length
     * - Odd length comments → Telegram Bot topic
     * - Even length comments → Analytics topic (metadata + individual comments)
     */
    public void processComments(String videoUrl, ChannelMetadata channelMetadata,
                               java.util.List<CommentData> comments) {

        boolean hasEvenComment = false;
        int oddCommentCount = 0;
        int evenCommentCount = 0;

        // First pass: check if there are any even-length comments
        for (CommentData comment : comments) {
            if (comment.isEvenLength()) {
                hasEvenComment = true;
                evenCommentCount++;
            } else if (comment.isOddLength()) {
                oddCommentCount++;
            }
        }

        // Send metadata to analytics if there's at least one even-length comment
        if (hasEvenComment) {
            sendEvenMetadataToAnalytics(channelMetadata);
            logger.info("Sent channel metadata to analytics for video: {} (found {} even-length comments)",
                       channelMetadata.getVideoId(), evenCommentCount);
        }

        // Send all comments to their respective topics
        for (CommentData comment : comments) {
            if (comment.isOddLength()) {
                sendOddCommentToTelegram(comment);
            } else if (comment.isEvenLength()) {
                sendEvenCommentToAnalytics(comment);
            }
        }

        logger.info("Processed {} odd-length comments for Telegram, {} even-length comments for analytics (video: {})",
                   oddCommentCount, evenCommentCount, channelMetadata.getVideoId());
    }

    /**
     * Send odd-length comment data to Telegram Bot topic
     */
    private void sendOddCommentToTelegram(CommentData commentData) {
        
        try {
            CompletableFuture<SendResult<String, Object>> future = 
                kafkaTemplate.send(ODD_COMMENTS_TOPIC, commentData.getVideoId(), commentData);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    logger.debug("Sent odd comment to Telegram topic - Video: {}, Comment length: {}", 
                               commentData.getVideoTitle(), commentData.getCommentLength());
                } else {
                    logger.error("Failed to send odd comment to Telegram topic - Video: {}", 
                                commentData.getVideoTitle(), ex);
                }
            });
            
        } catch (Exception e) {
            logger.error("Error sending odd comment to Kafka - Video: {}", 
                        commentData.getVideoTitle(), e);
        }
    }

    /**
     * Send even-length comment data to Analytics topic
     */
    private void sendEvenCommentToAnalytics(CommentData commentData) {
        try {
            CompletableFuture<SendResult<String, Object>> future =
                kafkaTemplate.send(EVEN_COMMENTS_TOPIC, commentData.getVideoId(), commentData);

            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    logger.debug("Sent even comment to Analytics topic - Video: {}, Comment length: {}",
                               commentData.getVideoTitle(), commentData.getCommentLength());
                } else {
                    logger.error("Failed to send even comment to Analytics topic - Video: {}",
                                commentData.getVideoTitle(), ex);
                }
            });

        } catch (Exception e) {
            logger.error("Error sending even comment to Kafka - Video: {}",
                        commentData.getVideoTitle(), e);
        }
    }



    /**
     * Send channel metadata to Analytics topic (for even-length comments)
     * Only sends once per video, regardless of how many even-length comments exist
     */
    private void sendEvenMetadataToAnalytics(ChannelMetadata channelMetadata) {
        try {
            CompletableFuture<SendResult<String, Object>> future =
                kafkaTemplate.send(EVEN_METADATA_TOPIC, channelMetadata.getChannelId(), channelMetadata);

            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    logger.debug("Sent channel metadata to Analytics topic - Channel: {} (Video: {})",
                               channelMetadata.getChannelName(), channelMetadata.getVideoId());
                } else {
                    logger.error("Failed to send metadata to Analytics topic - Channel: {}",
                                channelMetadata.getChannelName(), ex);
                }
            });

        } catch (Exception e) {
            logger.error("Error sending metadata to Kafka - Channel: {}",
                        channelMetadata.getChannelName(), e);
        }
    }

    /**
     * Send a test message to verify Kafka connectivity
     */
    public void sendTestMessage() {
        try {
            kafkaTemplate.send("test-topic", "test-key", "Hello from YouTube Analytics Producer!");
            logger.info("Test message sent to Kafka successfully");
        } catch (Exception e) {
            logger.error("Failed to send test message to Kafka", e);
        }
    }

    /**
     * Get topic names (useful for configuration verification)
     */
    public String getOddCommentsTopic() {
        return ODD_COMMENTS_TOPIC;
    }

    public String getEvenMetadataTopic() {
        return EVEN_METADATA_TOPIC;
    }

    /**
     * Send clear signal to consumer apps when new submission is made
     */
    public void sendClearSignal(String processingSession) {
        try {
            Map<String, String> clearMessage = Map.of(
                "action", "clear",
                "processingSession", processingSession,
                "timestamp", java.time.LocalDateTime.now().toString()
            );
            
            CompletableFuture<SendResult<String, Object>> future = 
                kafkaTemplate.send(CONTROL_TOPIC, "clear", clearMessage);
                
            future.whenComplete((result, exception) -> {
                if (exception == null) {
                    logger.info("Successfully sent clear signal for session: {}", processingSession);
                } else {
                    logger.error("Failed to send clear signal for session: {}", processingSession, exception);
                }
            });
            
        } catch (Exception e) {
            logger.error("Error sending clear signal for session: {}", processingSession, e);
        }
    }
} 