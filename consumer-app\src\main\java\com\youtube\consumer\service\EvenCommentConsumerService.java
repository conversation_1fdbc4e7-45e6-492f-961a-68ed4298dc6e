package com.youtube.consumer.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import com.youtube.shared.model.CommentData;

@Service
public class EvenCommentConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(EvenCommentConsumerService.class);

    // Store even comments by video ID
    private final Map<String, List<CommentData>> videoCommentsMap = new ConcurrentHashMap<>();
    private volatile boolean newDataReceived = false;

    /**
     * Consume even-length comments from Kafka and store them by video
     */
    @KafkaListener(topics = "youtube-even-comments", groupId = "youtube-analytics-comments-group")
    public void consumeEvenComment(CommentData commentData) {
        logger.info("Received even comment from Kafka: {} chars from video: {}", 
                   commentData.getCommentLength(), 
                   commentData.getVideoTitle() != null ? commentData.getVideoTitle() : commentData.getVideoUrl());

        try {
            // Verify this is indeed an even-length comment
            if (!commentData.isEvenLength()) {
                logger.warn("Received comment that is not even-length: {} chars. Skipping.", 
                           commentData.getCommentLength());
                return;
            }

            String videoId = commentData.getVideoId();
            if (videoId != null) {
                // Store comment for this video
                List<CommentData> videoComments = videoCommentsMap.computeIfAbsent(videoId, k -> new ArrayList<>());
                videoComments.add(commentData);
                
                logger.info("Stored even comment for video: {} (total even comments for this video: {})", 
                           videoId, videoComments.size());
            } else {
                logger.warn("Received comment without video ID, skipping");
            }

            // Mark that new data has been received
            newDataReceived = true;
            
            logger.debug("Successfully processed even comment. Total videos with even comments: {}", 
                       videoCommentsMap.size());

        } catch (Exception e) {
            logger.error("Error processing even comment from Kafka", e);
        }
    }

    /**
     * Get all even comments for a specific video
     */
    public List<CommentData> getEvenCommentsForVideo(String videoId) {
        return videoCommentsMap.getOrDefault(videoId, new ArrayList<>());
    }

    /**
     * Get the first even comment for a specific video (for display purposes)
     */
    public CommentData getFirstEvenCommentForVideo(String videoId) {
        List<CommentData> comments = videoCommentsMap.get(videoId);
        return (comments != null && !comments.isEmpty()) ? comments.get(0) : null;
    }

    /**
     * Get all video IDs that have even comments
     */
    public List<String> getVideosWithEvenComments() {
        return new ArrayList<>(videoCommentsMap.keySet());
    }

    /**
     * Clear all stored even comments (when new submission arrives)
     */
    public void clearAll() {
        logger.info("=== CLEARING ALL EVEN COMMENTS DATA ===");
        int videoCount = videoCommentsMap.size();
        int totalComments = videoCommentsMap.values().stream().mapToInt(List::size).sum();

        // Clear all data structures
        videoCommentsMap.clear();
        newDataReceived = true; // Set to true to trigger UI refresh

        logger.info("Successfully cleared even comments for {} videos ({} total comments). Ready for new data.",
                   videoCount, totalComments);
        logger.info("=== EVEN COMMENTS RESET COMPLETE ===");
    }

    /**
     * Check if new data has been received since last check
     */
    public boolean hasNewData() {
        return newDataReceived;
    }

    /**
     * Mark data as processed (reset new data flag)
     */
    public void markDataAsProcessed() {
        newDataReceived = false;
    }

    /**
     * Get summary statistics
     */
    public Map<String, Object> getStats() {
        int totalVideos = videoCommentsMap.size();
        int totalComments = videoCommentsMap.values().stream().mapToInt(List::size).sum();
        
        return Map.of(
            "totalVideosWithEvenComments", totalVideos,
            "totalEvenComments", totalComments,
            "newDataReceived", newDataReceived
        );
    }
}
