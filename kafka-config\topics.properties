# ========================================================================================
# Kafka Topics Configuration - YouTube Comment Bot Project
# ========================================================================================
# This file defines all topics used in the YouTube Comment Bot system
# Topic names are preserved exactly as they are in the Java code

# ======================
# MAIN APPLICATION TOPICS
# ======================

# Topic for odd-length comments (sent to Telegram Bot)
# Producer: youtube-producer → Consumer: telegram-bot
youtube-odd-comments.partitions=3
youtube-odd-comments.replication.factor=1
youtube-odd-comments.cleanup.policy=delete
youtube-odd-comments.retention.ms=604800000
youtube-odd-comments.segment.ms=86400000

# Topic for even-length video metadata (sent to Consumer App)
# Producer: youtube-producer → Consumer: consumer-app
youtube-even-metadata.partitions=3
youtube-even-metadata.replication.factor=1
youtube-even-metadata.cleanup.policy=delete
youtube-even-metadata.retention.ms=604800000
youtube-even-metadata.segment.ms=86400000

# Topic for even-length comments (sent to Consumer App)
# Producer: youtube-producer → Consumer: consumer-app
youtube-even-comments.partitions=3
youtube-even-comments.replication.factor=1
youtube-even-comments.cleanup.policy=delete
youtube-even-comments.retention.ms=604800000
youtube-even-comments.segment.ms=86400000

# Topic for control messages (clear signals, session management)
# Producer: youtube-producer → Consumers: telegram-bot, consumer-app
youtube-control.partitions=1
youtube-control.replication.factor=1
youtube-control.cleanup.policy=delete
youtube-control.retention.ms=86400000
youtube-control.segment.ms=3600000

# ======================
# TESTING TOPICS
# ======================

# Topic for testing Kafka connectivity
test-topic.partitions=1
test-topic.replication.factor=1
test-topic.cleanup.policy=delete
test-topic.retention.ms=3600000

# ======================
# TOPIC CREATION COMMANDS
# ======================
# Use these commands to manually create topics if needed:
#
# kafka-topics.sh --create --topic youtube-odd-comments --partitions 3 --replication-factor 1 --bootstrap-server kafka:9092
# kafka-topics.sh --create --topic youtube-even-metadata --partitions 3 --replication-factor 1 --bootstrap-server kafka:9092
# kafka-topics.sh --create --topic youtube-even-comments --partitions 3 --replication-factor 1 --bootstrap-server kafka:9092
# kafka-topics.sh --create --topic youtube-control --partitions 1 --replication-factor 1 --bootstrap-server kafka:9092
# kafka-topics.sh --create --topic test-topic --partitions 1 --replication-factor 1 --bootstrap-server kafka:9092

# ======================
# TOPIC DESCRIPTIONS
# ======================
# youtube-odd-comments: Contains CommentData objects with odd character length
# youtube-even-metadata: Contains ChannelMetadata objects for videos with even-length comments
# youtube-even-comments: Contains CommentData objects with even character length
# youtube-control: Contains control messages for system coordination
# test-topic: Used for connectivity testing
