services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: youtube-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpass
      MYSQL_DATABASE: youtube_analytics
      MYSQL_USER: youtube_user
      MY<PERSON><PERSON>_PASSWORD: youtube_pass
    ports:
      - "3309:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - youtube-network

  # Zookeeper (required for Kafka)
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: youtube-zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - youtube-network

  # Kafka
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: youtube-kafka
    restart: unless-stopped
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
    volumes:
      - kafka_data:/var/lib/kafka/data
      - ./kafka-config:/opt/kafka/config/custom:ro
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "9092"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - youtube-network

  # Producer Web App (Using Docker Hub Image)
  producer-app:
    image: shakalakahaha/youtube-producer:latest
    container_name: youtube-producer
    restart: unless-stopped
    ports:
      - "8081:8081"
    depends_on:
      mysql:
        condition: service_started
      kafka:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: youtube_analytics
      DB_USER: youtube_user
      DB_PASSWORD: youtube_pass
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - youtube-network

  # Consumer Web App (Using Docker Hub Image)
  consumer-app:
    image: shakalakahaha/youtube-consumer:latest
    container_name: youtube-consumer
    restart: unless-stopped
    ports:
      - "8083:8082"
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - youtube-network

  # Telegram Bot (Using Docker Hub Image)
  telegram-bot:
    image: shakalakahaha/youtube-telegram:latest
    container_name: youtube-telegram-bot
    restart: unless-stopped
    ports:
      - "8084:8084"
    depends_on:
      mysql:
        condition: service_started
      kafka:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: youtube_analytics
      DB_USER: youtube_user
      DB_PASSWORD: youtube_pass
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - youtube-network

volumes:
  mysql_data:
  kafka_data:
  zookeeper_data:
  zookeeper_logs:

networks:
  youtube-network:
    driver: bridge
