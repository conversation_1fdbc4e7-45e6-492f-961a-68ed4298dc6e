package com.youtube.shared.model;

import java.time.LocalDateTime;

public class CommentData {
    private String commentId;
    private String videoUrl;
    private String videoId;
    private String videoTitle;
    private String videoThumbnail;
    private String commentText;
    private int commentLength;
    private Long likesCount;
    private String authorName;
    private ChannelMetadata channelMetadata;
    private LocalDateTime timestamp;

    // Default constructor
    public CommentData() {}

    // Constructor with all fields
    public CommentData(String commentId, String videoUrl, String videoId, String videoTitle, String videoThumbnail,
                      String commentText, Long likesCount, String authorName,
                      ChannelMetadata channelMetadata) {
        this.commentId = commentId;
        this.videoUrl = videoUrl;
        this.videoId = videoId;
        this.videoTitle = videoTitle;
        this.videoThumbnail = videoThumbnail;
        this.commentText = commentText;
        this.commentLength = commentText != null ? commentText.length() : 0;
        this.likesCount = likesCount;
        this.authorName = authorName;
        this.channelMetadata = channelMetadata;
        this.timestamp = LocalDateTime.now();
    }

    // Simplified constructor for backward compatibility
    public CommentData(String videoUrl, String commentText, Long likesCount,
                      ChannelMetadata channelMetadata) {
        this.videoUrl = videoUrl;
        this.commentText = commentText;
        this.commentLength = commentText != null ? commentText.length() : 0;
        this.likesCount = likesCount;
        this.channelMetadata = channelMetadata;
        this.timestamp = LocalDateTime.now();
    }

    // Getters and Setters
    public String getCommentId() {
        return commentId;
    }

    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getVideoTitle() {
        return videoTitle;
    }

    public void setVideoTitle(String videoTitle) {
        this.videoTitle = videoTitle;
    }

    public String getVideoThumbnail() {
        return videoThumbnail;
    }

    public void setVideoThumbnail(String videoThumbnail) {
        this.videoThumbnail = videoThumbnail;
    }

    public String getCommentText() {
        return commentText;
    }

    public void setCommentText(String commentText) {
        this.commentText = commentText;
        this.commentLength = commentText != null ? commentText.length() : 0;
    }

    public int getCommentLength() {
        return commentLength;
    }

    public void setCommentLength(int commentLength) {
        this.commentLength = commentLength;
    }

    public Long getLikesCount() {
        return likesCount;
    }

    public void setLikesCount(Long likesCount) {
        this.likesCount = likesCount;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public ChannelMetadata getChannelMetadata() {
        return channelMetadata;
    }

    public void setChannelMetadata(ChannelMetadata channelMetadata) {
        this.channelMetadata = channelMetadata;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    // Utility methods
    public boolean isOddLength() {
        return commentLength % 2 != 0;
    }

    public boolean isEvenLength() {
        return commentLength % 2 == 0;
    }

    @Override
    public String toString() {
        return "CommentData{" +
                "commentId='" + commentId + '\'' +
                ", videoUrl='" + videoUrl + '\'' +
                ", videoId='" + videoId + '\'' +
                ", videoTitle='" + videoTitle + '\'' +
                ", commentText='" + commentText + '\'' +
                ", commentLength=" + commentLength +
                ", likesCount=" + likesCount +
                ", authorName='" + authorName + '\'' +
                ", channelMetadata=" + channelMetadata +
                ", timestamp=" + timestamp +
                '}';
    }
} 