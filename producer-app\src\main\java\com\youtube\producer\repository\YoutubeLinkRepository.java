package com.youtube.producer.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.youtube.producer.entity.YoutubeLink;

@Repository
public interface YoutubeLinkRepository extends JpaRepository<YoutubeLink, Long> {

    /**
     * Find all active YouTube links
     */
    List<YoutubeLink> findByIsActiveTrue();

    /**
     * Find YouTube link by video ID
     */
    Optional<YoutubeLink> findByVideoId(String videoId);

    /**
     * Check if a YouTube URL already exists
     */
    boolean existsByVideoUrl(String videoUrl);

    /**
     * Check if a video ID already exists
     */
    boolean existsByVideoId(String videoId);

    /**
     * Count active links
     */
    @Query("SELECT COUNT(y) FROM YoutubeLink y WHERE y.isActive = true")
    long countActiveLinks();

    /**
     * Find the most recently added links
     */
    @Query("SELECT y FROM YoutubeLink y WHERE y.isActive = true ORDER BY y.createdAt DESC")
    List<YoutubeLink> findRecentActiveLinks();



    /**
     * Find links by processing session
     */
    List<YoutubeLink> findByProcessingSession(String processingSession);
} 