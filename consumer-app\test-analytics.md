# Consumer App Analytics Testing Guide

## Quick Test Steps

1. **Start the Consumer App**
   ```bash
   mvn spring-boot:run
   ```
   Access at: http://localhost:8082

2. **Add Test Data** (via browser console or REST client)
   ```bash
   curl -X POST http://localhost:8082/add-test-data
   ```
   Or open browser dev tools and run:
   ```javascript
   fetch('/add-test-data', { method: 'POST' })
     .then(response => response.json())
     .then(data => console.log(data));
   ```

3. **Test Analytics Calculation**
   - Click "Calculate" button on the dashboard
   - Check browser console for debug logs
   - Should see 6 channel comparison cards

4. **Debug Analytics Data** (via browser console)
   ```javascript
   fetch('/debug-analytics')
     .then(response => response.json())
     .then(data => console.log('Analytics Data:', data));
   ```

## Expected Results

### Test Data Created:
- **TechChannel**: 1M subscribers, 500 videos, 10K comments, 50K likes
- **CookingChannel**: 500K subscribers, 300 videos, 15K comments, 30K likes  
- **MusicChannel**: 2M subscribers, 800 videos, 25K comments, 100K likes

### Expected Winners:
- 🥇 **Most Subscribers**: MusicChannel (2M)
- 🎬 **Most Videos**: MusicChannel (800)
- 💬 **Most Comments**: MusicChannel (25K)
- ❤️ **Most Likes**: MusicChannel (100K)
- 👁️ **Highest Avg Views**: MusicChannel (125 likes/video)
- 🔥 **Best Engagement**: CookingChannel (9.0% engagement rate)

## Troubleshooting

If analytics don't show:
1. Check browser console for error messages
2. Check if test data was added: http://localhost:8082/status
3. Try debug endpoint: http://localhost:8082/debug-analytics
4. Clear data and retry: POST to /clear then /add-test-data

## Analytics Calculations

- **Engagement Rate** = (Likes + Comments) / Subscribers × 100
- **Avg Views** = Total Likes / Total Videos (proxy for views)
- **Comparisons** = Find channel with highest value in each category 