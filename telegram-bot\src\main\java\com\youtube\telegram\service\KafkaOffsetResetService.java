package com.youtube.telegram.service;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.DeleteConsumerGroupsResult;
import org.apache.kafka.clients.admin.ListConsumerGroupsResult;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.KafkaFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

@Service
public class KafkaOffsetResetService {

    private static final Logger logger = LoggerFactory.getLogger(KafkaOffsetResetService.class);
    
    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;
    
    /**
     * Reset Kafka consumer group offsets by deleting the consumer groups
     * This forces them to start fresh from the latest offset
     */
    public void resetTelegramConsumerOffsets() {
        logger.info("=== RESETTING TELEGRAM KAFKA CONSUMER OFFSETS ===");
        
        try {
            Properties adminProps = new Properties();
            adminProps.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
            adminProps.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, 10000);
            adminProps.put(AdminClientConfig.DEFAULT_API_TIMEOUT_MS_CONFIG, 10000);
            
            try (AdminClient adminClient = AdminClient.create(adminProps)) {
                
                // List of consumer groups to reset
                String[] consumerGroups = {
                    "youtube-telegram-group-v2",           // For odd comments
                    "youtube-telegram-control-group-v3"    // For control messages
                };
                
                for (String groupId : consumerGroups) {
                    try {
                        logger.info("Attempting to reset consumer group: {}", groupId);
                        
                        // Check if consumer group exists
                        ListConsumerGroupsResult listResult = adminClient.listConsumerGroups();
                        boolean groupExists = listResult.all().get(5, TimeUnit.SECONDS)
                            .stream()
                            .anyMatch(group -> group.groupId().equals(groupId));
                        
                        if (groupExists) {
                            // Delete the consumer group to reset offsets
                            DeleteConsumerGroupsResult deleteResult = adminClient.deleteConsumerGroups(
                                Collections.singletonList(groupId)
                            );
                            
                            // Wait for deletion to complete
                            KafkaFuture<Void> future = deleteResult.deletedGroups().get(groupId);
                            future.get(10, TimeUnit.SECONDS);
                            
                            logger.info("Successfully reset consumer group: {}", groupId);
                        } else {
                            logger.info("Consumer group {} does not exist, no reset needed", groupId);
                        }
                        
                    } catch (Exception e) {
                        logger.warn("Could not reset consumer group {}: {} (this is normal if group doesn't exist)", 
                                   groupId, e.getMessage());
                    }
                }
                
                logger.info("=== TELEGRAM KAFKA CONSUMER OFFSET RESET COMPLETE ===");
                
            }
        } catch (Exception e) {
            logger.error("Error resetting Kafka consumer offsets", e);
        }
    }
    
    /**
     * Reset all consumer groups for a fresh start
     */
    public void resetAllConsumerOffsets() {
        logger.info("=== RESETTING ALL KAFKA CONSUMER OFFSETS FOR FRESH START ===");
        
        try {
            Properties adminProps = new Properties();
            adminProps.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
            adminProps.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, 10000);
            adminProps.put(AdminClientConfig.DEFAULT_API_TIMEOUT_MS_CONFIG, 10000);
            
            try (AdminClient adminClient = AdminClient.create(adminProps)) {
                
                // List all consumer groups to reset
                String[] allConsumerGroups = {
                    "youtube-telegram-group-v2",           // Telegram odd comments
                    "youtube-telegram-control-group-v3",   // Telegram control messages
                    "youtube-analytics-group-v2",          // Consumer app metadata
                    "youtube-analytics-control-group"      // Consumer app control messages
                };
                
                for (String groupId : allConsumerGroups) {
                    try {
                        logger.info("Attempting to reset consumer group: {}", groupId);
                        
                        // Check if consumer group exists
                        ListConsumerGroupsResult listResult = adminClient.listConsumerGroups();
                        boolean groupExists = listResult.all().get(5, TimeUnit.SECONDS)
                            .stream()
                            .anyMatch(group -> group.groupId().equals(groupId));
                        
                        if (groupExists) {
                            // Delete the consumer group to reset offsets
                            DeleteConsumerGroupsResult deleteResult = adminClient.deleteConsumerGroups(
                                Collections.singletonList(groupId)
                            );
                            
                            // Wait for deletion to complete
                            KafkaFuture<Void> future = deleteResult.deletedGroups().get(groupId);
                            future.get(10, TimeUnit.SECONDS);
                            
                            logger.info("Successfully reset consumer group: {}", groupId);
                        } else {
                            logger.info("Consumer group {} does not exist, no reset needed", groupId);
                        }
                        
                    } catch (Exception e) {
                        logger.warn("Could not reset consumer group {}: {} (this is normal if group doesn't exist)", 
                                   groupId, e.getMessage());
                    }
                }
                
                logger.info("=== ALL KAFKA CONSUMER OFFSET RESET COMPLETE ===");
                
            }
        } catch (Exception e) {
            logger.error("Error resetting all Kafka consumer offsets", e);
        }
    }
}
