package com.youtube.telegram.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import com.youtube.shared.model.CommentData;

@Service
public class CommentConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(CommentConsumerService.class);

    private final VideoCommentBatcher videoCommentBatcher;

    public CommentConsumerService(VideoCommentBatcher videoCommentBatcher) {
        this.videoCommentBatcher = videoCommentBatcher;
    }

    /**
     * Consume odd-length comments from Kafka and batch them by video
     */
    @KafkaListener(topics = "youtube-odd-comments", groupId = "youtube-telegram-group-v2")
    public void consumeOddLengthComment(CommentData commentData) {
        logger.info("Received odd-length comment from Kafka: {} chars from video: {}", 
                   commentData.getCommentLength(), 
                   commentData.getVideoTitle() != null ? commentData.getVideoTitle() : commentData.getVideoUrl());

        try {
            // Verify this is indeed an odd-length comment
            if (!commentData.isOddLength()) {
                logger.warn("Received comment that is not odd-length: {} chars. Skipping.", 
                           commentData.getCommentLength());
                return;
            }

            // Add to video batch instead of sending immediately
            videoCommentBatcher.addComment(commentData);
            
            logger.debug("Added comment to video batch: {} chars", 
                       commentData.getCommentLength());

        } catch (Exception e) {
            logger.error("Error processing odd-length comment from Kafka", e);
        }
    }
} 