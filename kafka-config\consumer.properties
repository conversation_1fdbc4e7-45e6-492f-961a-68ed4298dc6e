# ========================================================================================
# Kafka Consumer Configuration - YouTube Comment Bot Project
# ========================================================================================
# This file contains consumer settings migrated from application.yml files
# Used by: telegram-bot and consumer-app services

# Bootstrap Configuration
bootstrap.servers=kafka:9092

# ======================
# TELEGRAM BOT CONSUMER SETTINGS
# ======================
# Migrated from telegram-bot/src/main/resources/application.yml

# Primary Consumer Group (for odd comments)
telegram.group.id=youtube-telegram-group-v3
telegram.auto.offset.reset=latest
telegram.enable.auto.commit=true
telegram.auto.commit.interval.ms=1000

# Deserialization Settings for Telegram Bot
telegram.key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
telegram.value.deserializer=org.springframework.kafka.support.serializer.JsonDeserializer

# JSON Deserialization Settings for Telegram Bot
telegram.spring.json.trusted.packages=*
telegram.spring.json.default.type=com.youtube.shared.model.CommentData
telegram.spring.json.use.type.headers=false
telegram.spring.json.value.default.type=com.youtube.shared.model.CommentData

# Control Messages Consumer Group (for telegram bot)
telegram.control.group.id=youtube-telegram-control-group-v3

# Legacy Consumer Groups (for offset reset service)
telegram.legacy.group.id=youtube-telegram-group-v2

# ======================
# CONSUMER APP SETTINGS
# ======================
# Migrated from consumer-app/src/main/resources/application.yml

# Primary Consumer Group (for metadata)
analytics.group.id=youtube-analytics-group-v3
analytics.auto.offset.reset=latest
analytics.enable.auto.commit=true
analytics.auto.commit.interval.ms=1000

# Deserialization Settings for Consumer App
analytics.key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
analytics.value.deserializer=org.springframework.kafka.support.serializer.JsonDeserializer

# JSON Deserialization Settings for Consumer App
analytics.spring.json.trusted.packages=*
analytics.spring.json.use.type.headers=true

# Specific Consumer Groups for Different Topics
analytics.metadata.group.id=youtube-analytics-group-v2
analytics.comments.group.id=youtube-analytics-comments-group
analytics.control.group.id=youtube-analytics-control-group

# ======================
# COMMON CONSUMER SETTINGS
# ======================
# Applied to all consumers

# Session and Heartbeat
session.timeout.ms=30000
heartbeat.interval.ms=3000

# Fetch Settings
fetch.min.bytes=1
fetch.max.wait.ms=500
max.partition.fetch.bytes=1048576

# Processing Settings
max.poll.records=500
max.poll.interval.ms=300000

# Error Handling
retry.backoff.ms=100

# Metrics
metrics.sample.window.ms=30000
metrics.num.samples=2

# Security (if needed in future)
# security.protocol=PLAINTEXT

# ======================
# CONSUMER GROUP DESCRIPTIONS
# ======================
# youtube-telegram-group-v3: Main telegram bot consumer for odd comments
# youtube-telegram-control-group-v3: Telegram bot control message consumer
# youtube-telegram-group-v2: Legacy telegram consumer (for offset reset)
# youtube-analytics-group-v3: Main consumer app consumer
# youtube-analytics-group-v2: Consumer app metadata consumer
# youtube-analytics-comments-group: Consumer app comments consumer
# youtube-analytics-control-group: Consumer app control message consumer
