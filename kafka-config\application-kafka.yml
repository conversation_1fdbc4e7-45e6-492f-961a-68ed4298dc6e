# ========================================================================================
# Centralized Kafka Configuration for Spring Boot Applications
# ========================================================================================
# This file contains all Kafka settings that can be imported by Spring Boot apps
# Import this in your application.yml with: spring.config.import: classpath:kafka-config/application-kafka.yml

spring:
  kafka:
    # Bootstrap servers - same for all applications
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka:9092}
    
    # Producer configuration (for producer-app)
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        spring.json.add.type.headers: true
        # Performance settings
        acks: 1
        retries: 3
        batch.size: 16384
        linger.ms: 5
        compression.type: snappy
        enable.idempotence: true
        max.in.flight.requests.per.connection: 5
    
    # Consumer configuration (base settings for all consumers)
    consumer:
      auto-offset-reset: latest
      enable-auto-commit: true
      auto-commit-interval: 1000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
        # Performance settings
        session.timeout.ms: 30000
        heartbeat.interval.ms: 3000
        fetch.min.bytes: 1
        fetch.max.wait.ms: 500
        max.partition.fetch.bytes: 1048576
        max.poll.records: 500
        max.poll.interval.ms: 300000

# ========================================================================================
# Application-Specific Kafka Profiles
# ========================================================================================

---
# Profile for telegram-bot application
spring:
  config:
    activate:
      on-profile: telegram-kafka
  kafka:
    consumer:
      group-id: youtube-telegram-group-v3
      properties:
        spring.json.default.type: "com.youtube.shared.model.CommentData"
        spring.json.use.type.headers: false
        spring.json.value.default.type: "com.youtube.shared.model.CommentData"

---
# Profile for consumer-app application  
spring:
  config:
    activate:
      on-profile: consumer-kafka
  kafka:
    consumer:
      group-id: youtube-analytics-group-v3
      properties:
        spring.json.use.type.headers: true

---
# Profile for producer-app application
spring:
  config:
    activate:
      on-profile: producer-kafka
  kafka:
    # Producer-specific settings already defined in base configuration
    template:
      default-topic: youtube-control

# ========================================================================================
# Topic Configuration Reference
# ========================================================================================
# Topics used in the system (defined in topics.properties):
# - youtube-odd-comments: Odd-length comments → Telegram Bot
# - youtube-even-metadata: Video metadata → Consumer App  
# - youtube-even-comments: Even-length comments → Consumer App
# - youtube-control: Control messages → Both consumers
# - test-topic: Testing connectivity

# ========================================================================================
# Consumer Group Reference
# ========================================================================================
# Consumer groups used in @KafkaListener annotations:
# - youtube-telegram-group-v3: Main telegram bot consumer
# - youtube-telegram-control-group-v3: Telegram control messages
# - youtube-telegram-group-v2: Legacy telegram consumer (for offset reset)
# - youtube-analytics-group-v3: Main consumer app
# - youtube-analytics-group-v2: Consumer app metadata
# - youtube-analytics-comments-group: Consumer app comments
# - youtube-analytics-control-group: Consumer app control
