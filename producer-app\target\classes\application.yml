spring:
  application:
    name: youtube-producer

  # Import centralized Kafka configuration
  config:
    import:
      - optional:file:./kafka-config/application-kafka.yml
  profiles:
    include: producer-kafka

  # Database configuration
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3309}/${DB_NAME:youtube_analytics}
    username: ${DB_USER:youtube_user}
    password: ${DB_PASSWORD:youtube_pass}
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # Kafka configuration (now centralized in kafka-config/application-kafka.yml)
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        spring.json.add.type.headers: true

# YouTube API configuration
youtube:
  api:
    key: ${YOUTUBE_API_KEY}
    base-url: https://www.googleapis.com/youtube/v3

# Scheduling configuration
scheduler:
  youtube:
    fetch-interval: 30000  # 30 seconds in milliseconds

# Server configuration
server:
  port: 8081

# Logging configuration
logging:
  level:
    com.youtube.producer: DEBUG
    org.springframework.kafka: INFO
    org.hibernate.SQL: DEBUG 