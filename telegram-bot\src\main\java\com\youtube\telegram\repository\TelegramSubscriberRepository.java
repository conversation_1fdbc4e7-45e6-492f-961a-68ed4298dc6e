package com.youtube.telegram.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.youtube.telegram.entity.TelegramSubscriber;

@Repository
public interface TelegramSubscriberRepository extends JpaRepository<TelegramSubscriber, Long> {

    /**
     * Find subscriber by chat ID
     */
    Optional<TelegramSubscriber> findByChatId(String chatId);

    /**
     * Find all active subscribers
     */
    List<TelegramSubscriber> findByIsActiveTrue();

    /**
     * Count active subscribers
     */
    @Query("SELECT COUNT(t) FROM TelegramSubscriber t WHERE t.isActive = true")
    long countActiveSubscribers();

    /**
     * Check if chat ID exists and is active
     */
    boolean existsByChatIdAndIsActiveTrue(String chatId);

    /**
     * Find by username (case insensitive)
     */
    Optional<TelegramSubscriber> findByUserNameIgnoreCase(String userName);
} 