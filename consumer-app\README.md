# YouTube Analytics Consumer Dashboard

## Overview
The Consumer App provides a comprehensive analytics dashboard for YouTube channel data. It consumes channel metadata from Kafka and provides real-time analytics and comparisons.

## Features

### 📊 Real-time Analytics
- **Overall Engagement Rate**: Calculated as (Total Likes + Total Comments) / Total Subscribers × 100
- **Average Likes per Video**: Total channel likes divided by total channel videos
- **Channel Statistics**: Aggregated data across all unique channels

### 🏆 Channel Comparisons
When you click "Calculate Analytics", the dashboard displays winners in 6 categories:

1. **Most Subscribers** - Channel with the highest subscriber count
2. **Most Videos** - Channel with the most total videos
3. **Most Comments** - Channel with the highest total comment count
4. **Most Likes** - Channel with the most total likes
5. **Highest Average Views** - Channel with best average likes per video ratio
6. **Best Engagement Rate** - Channel with the highest engagement percentage

### 📈 Channel Metadata Display
- Real-time display of all unique channels from submitted videos
- Subscriber counts, video counts, comments, and likes
- Automatic deduplication (multiple videos from same channel = 1 channel entry)

## How to Use

1. **Start the Application**
   ```bash
   mvn spring-boot:run
   ```
   Access at: http://localhost:8082

2. **Submit Videos** 
   - Use the Producer App (port 8081) to submit YouTube video URLs
   - The Consumer App will automatically receive and process channel metadata

3. **View Analytics**
   - Click the "Calculate Analytics" button to see comprehensive comparisons
   - Data refreshes automatically every 30 seconds
   - Use "Refresh" button for manual updates
   - Use "Clear" button to reset all data

## API Endpoints

- `GET /` - Main dashboard
- `GET /analytics` - Get analytics data (JSON)
- `GET /status` - Get current status
- `POST /calculate-analytics` - Force analytics calculation
- `POST /clear` - Clear all stored data

## Kafka Topics Consumed

- `youtube-even-metadata` - Channel metadata for videos with even-length comments
- `youtube-control` - Control messages for data management

## Technical Details

- **Port**: 8082
- **Framework**: Spring Boot 3.1.5
- **Real-time Updates**: WebSocket + Auto-refresh
- **Data Storage**: In-memory with concurrent maps
- **UI**: Bootstrap 5 + Font Awesome icons

## Analytics Calculations

### Engagement Rate
```
Engagement Rate = ((Total Likes + Total Comments) / Total Subscribers) × 100
```

### Average Likes per Video
```
Avg Likes per Video = Total Channel Likes / Total Channel Videos
```

### Channel Deduplication
- Multiple videos from the same channel are treated as one channel
- Latest metadata updates override previous data
- Video count per channel is tracked separately

## Styling Features

- **Gradient Backgrounds**: Analytics (blue-purple), Comparisons (pink-red)
- **Trophy Icons**: Gold trophies for comparison winners
- **Hover Effects**: Cards scale and transform on hover
- **Responsive Design**: Works on desktop and mobile
- **Loading Animations**: Smooth transitions and spinners 