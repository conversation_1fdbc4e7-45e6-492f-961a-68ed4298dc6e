package com.youtube.telegram.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;

import com.youtube.telegram.service.YouTubeAnalyticsBot;

@Configuration
public class TelegramBotConfig {

    private static final Logger logger = LoggerFactory.getLogger(TelegramBotConfig.class);

    @Bean
    public TelegramBotsApi telegramBotsApi(YouTubeAnalyticsBot youTubeAnalyticsBot) {
        try {
            TelegramBotsApi botsApi = new TelegramBotsApi(DefaultBotSession.class);
            botsApi.registerBot(youTubeAnalyticsBot);
            logger.info("Telegram bot registered successfully");
            return botsApi;
        } catch (TelegramApiException e) {
            logger.error("Failed to register Telegram bot: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to register Telegram bot", e);
        }
    }
} 