package com.youtube.consumer.service;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
public class ControlMessageConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(ControlMessageConsumerService.class);

    private final ChannelMetadataConsumerService channelMetadataConsumerService;
    private final EvenCommentConsumerService evenCommentConsumerService;

    public ControlMessageConsumerService(ChannelMetadataConsumerService channelMetadataConsumerService,
                                        EvenCommentConsumerService evenCommentConsumerService) {
        this.channelMetadataConsumerService = channelMetadataConsumerService;
        this.evenCommentConsumerService = evenCommentConsumerService;
    }

    /**
     * Listen for control messages from producer app
     */
    @KafkaListener(topics = "youtube-control", groupId = "youtube-analytics-control-group")
    public void handleControlMessage(Map<String, String> message) {
        logger.info("Received control message: {}", message);

        try {
            String action = message.get("action");
            String processingSession = message.get("processingSession");
            
            if ("clear".equals(action)) {
                logger.info("Processing clear action for session: {}", processingSession);
                channelMetadataConsumerService.clearAll();
                evenCommentConsumerService.clearAll();
                logger.info("Successfully cleared all data (metadata and comments) for new processing session: {}", processingSession);
            } else {
                logger.warn("Unknown control action received: {}", action);
            }

        } catch (Exception e) {
            logger.error("Error processing control message: {}", message, e);
        }
    }
} 