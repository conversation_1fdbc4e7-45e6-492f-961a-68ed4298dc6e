package com.youtube.producer.service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.youtube.producer.entity.YoutubeLink;
import com.youtube.producer.repository.YoutubeLinkRepository;

@Service
@Transactional
public class YoutubeLinkService {

    private static final Logger logger = LoggerFactory.getLogger(YoutubeLinkService.class);
    private static final int MAX_LINKS = 5;

    private final YoutubeLinkRepository youtubeLinkRepository;
    private final KafkaProducerService kafkaProducerService;
    private final DatabaseClearingService databaseClearingService;

    public YoutubeLinkService(YoutubeLinkRepository youtubeLinkRepository,
                             KafkaProducerService kafkaProducerService,
                             DatabaseClearingService databaseClearingService) {
        this.youtubeLinkRepository = youtubeLinkRepository;
        this.kafkaProducerService = kafkaProducerService;
        this.databaseClearingService = databaseClearingService;
    }

    /**
     * Submit multiple YouTube links (exactly 5 required)
     * Creates new processing session and replaces old links
     */
    @Transactional
    public SubmissionResult submitLinks(List<String> videoUrls) {
        logger.info("Attempting to submit {} YouTube links", videoUrls.size());

        // Validate input
        ValidationResult validation = validateLinks(videoUrls);
        if (!validation.isValid()) {
            return new SubmissionResult(false, validation.getErrors(), new ArrayList<>());
        }

        List<YoutubeLink> savedLinks = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        // Create new processing session ID
        String processingSession = UUID.randomUUID().toString();
        logger.info("Creating new processing session: {}", processingSession);

        try {
            // Clear existing links first in a separate transaction
            databaseClearingService.clearAllLinks();
            
            // Send clear signal to consumer apps
            kafkaProducerService.sendClearSignal(processingSession);

            // Save each valid link with new processing session
            for (String url : videoUrls) {
                try {
                    YoutubeLink link = new YoutubeLink();
                    link.setVideoUrl(url.trim());
                    link.setProcessingSession(processingSession);
                    // No processing flags needed - we fetch fresh data every time
                    
                    YoutubeLink savedLink = youtubeLinkRepository.save(link);
                    savedLinks.add(savedLink);
                    
                    logger.info("Saved YouTube link: {} (Video ID: {}, Session: {})", 
                               savedLink.getVideoUrl(), savedLink.getVideoId(), processingSession);
                    
                } catch (Exception e) {
                    String error = "Failed to save URL: " + url + " - " + e.getMessage();
                    errors.add(error);
                    logger.error("Error saving YouTube link: {}", url, e);
                }
            }

            boolean success = errors.isEmpty() && savedLinks.size() == MAX_LINKS;
            
            if (success) {
                logger.info("Successfully submitted {} YouTube links with session {}", 
                           savedLinks.size(), processingSession);
            } else {
                logger.warn("Partial success: saved {}/{} links with {} errors", 
                           savedLinks.size(), MAX_LINKS, errors.size());
            }

            return new SubmissionResult(success, errors, savedLinks);
            
        } catch (Exception e) {
            logger.error("Error during link submission transaction", e);
            errors.add("Transaction error: " + e.getMessage());
            return new SubmissionResult(false, errors, savedLinks);
        }
    }

    /**
     * Update the last processed comment ID for a video to avoid duplicates
     */
    public void updateLastProcessedCommentId(String videoId, String commentId) {
        youtubeLinkRepository.findByVideoId(videoId).ifPresent(link -> {
            link.setLastProcessedCommentId(commentId);
            youtubeLinkRepository.save(link);
            logger.debug("Updated last processed comment ID for video: {} to: {}", videoId, commentId);
        });
    }

    /**
     * Validate the submitted links
     */
    private ValidationResult validateLinks(List<String> videoUrls) {
        List<String> errors = new ArrayList<>();

        // Check if exactly 5 links provided
        if (videoUrls == null || videoUrls.size() != MAX_LINKS) {
            errors.add("Exactly " + MAX_LINKS + " YouTube video URLs are required");
            return new ValidationResult(false, errors);
        }

        // Check each URL
        for (int i = 0; i < videoUrls.size(); i++) {
            String url = videoUrls.get(i);
            if (url == null || url.trim().isEmpty()) {
                errors.add("URL " + (i + 1) + " cannot be empty");
                continue;
            }

            url = url.trim();
            
            // Basic YouTube URL validation
            if (!isValidYouTubeUrl(url)) {
                errors.add("URL " + (i + 1) + " is not a valid YouTube URL: " + url);
            }
        }

        // Check for duplicates
        List<String> cleanUrls = videoUrls.stream()
            .filter(url -> url != null && !url.trim().isEmpty())
            .map(String::trim)
            .toList();
        
        long uniqueCount = cleanUrls.stream().distinct().count();
        if (uniqueCount != cleanUrls.size()) {
            errors.add("Duplicate URLs are not allowed");
        }

        return new ValidationResult(errors.isEmpty(), errors);
    }

    /**
     * Basic YouTube URL validation
     */
    private boolean isValidYouTubeUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        String lowerUrl = url.toLowerCase();
        return (lowerUrl.contains("youtube.com/watch?v=") || lowerUrl.contains("youtu.be/")) 
               && (lowerUrl.startsWith("http://") || lowerUrl.startsWith("https://"));
    }

    /**
     * Get all active YouTube links
     */
    public List<YoutubeLink> getAllActiveLinks() {
        return youtubeLinkRepository.findByIsActiveTrue();
    }

    /**
     * Get submission statistics
     */
    public SubmissionStats getStats() {
        long totalLinks = youtubeLinkRepository.count();
        long activeLinks = youtubeLinkRepository.countActiveLinks();
        return new SubmissionStats(totalLinks, activeLinks, MAX_LINKS);
    }

    // Data classes
    public static class SubmissionResult {
        private final boolean success;
        private final List<String> errors;
        private final List<YoutubeLink> savedLinks;

        public SubmissionResult(boolean success, List<String> errors, List<YoutubeLink> savedLinks) {
            this.success = success;
            this.errors = errors;
            this.savedLinks = savedLinks;
        }

        public boolean isSuccess() { return success; }
        public List<String> getErrors() { return errors; }
        public List<YoutubeLink> getSavedLinks() { return savedLinks; }
    }

    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;

        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors;
        }

        public boolean isValid() { return valid; }
        public List<String> getErrors() { return errors; }
    }

    public static class SubmissionStats {
        private final long totalLinks;
        private final long activeLinks;
        private final int maxAllowedLinks;

        public SubmissionStats(long totalLinks, long activeLinks, int maxAllowedLinks) {
            this.totalLinks = totalLinks;
            this.activeLinks = activeLinks;
            this.maxAllowedLinks = maxAllowedLinks;
        }

        public long getTotalLinks() { return totalLinks; }
        public long getActiveLinks() { return activeLinks; }
        public int getMaxAllowedLinks() { return maxAllowedLinks; }
        public boolean canSubmitMore() { return activeLinks < maxAllowedLinks; }
    }
} 